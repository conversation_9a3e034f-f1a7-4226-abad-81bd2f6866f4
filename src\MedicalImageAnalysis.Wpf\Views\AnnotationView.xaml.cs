using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Microsoft.Win32;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using MedicalImageAnalysis.Wpf.Services;
using MedicalImageAnalysis.Wpf.Controls;
using MedicalImageAnalysis.Wpf.Windows;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Entities;
using System.ComponentModel;

// WPF 类型别名已在 GlobalUsings.cs 中定义

namespace MedicalImageAnalysis.Wpf.Views
{
    /// <summary>
    /// AnnotationView.xaml 的交互逻辑
    /// </summary>
    public partial class AnnotationView : System.Windows.Controls.UserControl
    {
        private readonly ILogger<AnnotationView> _logger;
        private readonly GdcmDicomService _gdcmDicomService;
        private readonly GdcmImageProcessor _gdcmImageProcessor;
        private readonly ISmartAnnotationService _smartAnnotationService;
        private readonly IAnnotationService _annotationService;
        private BitmapSource? _currentImage;
        private bool _hasImage = false;
        private bool _isDicomImage = false;
        private string? _currentDicomFilePath;
        private string _currentAnnotationTool = "";
        private bool _isDrawing = false;
        private System.Windows.Point _startPoint;
        private Shape? _currentShape;
        private readonly ObservableCollection<AnnotationItem> _annotations;
        private readonly HashSet<string> _systemCategories;
        private double _currentConfidenceThreshold = 0.7;
        private CancellationTokenSource? _batchProcessingCancellationTokenSource;
        private List<System.IO.FileInfo> _currentFolderFiles = new();
        private string? _currentFolderPath;

        public AnnotationView()
        {
            InitializeComponent();

            // 从依赖注入容器获取服务
            var serviceProvider = ((App)System.Windows.Application.Current).ServiceProvider;
            _logger = serviceProvider?.GetService<ILogger<AnnotationView>>()
                     ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<AnnotationView>.Instance;

            _gdcmDicomService = serviceProvider?.GetService<GdcmDicomService>()
                               ?? new GdcmDicomService(serviceProvider?.GetService<ILogger<GdcmDicomService>>()!);

            _gdcmImageProcessor = serviceProvider?.GetService<GdcmImageProcessor>()
                                 ?? new GdcmImageProcessor(serviceProvider?.GetService<ILogger<GdcmImageProcessor>>()!);

            _smartAnnotationService = serviceProvider?.GetService<ISmartAnnotationService>()
                                     ?? throw new InvalidOperationException("ISmartAnnotationService not registered");

            _annotationService = serviceProvider?.GetService<IAnnotationService>()
                               ?? throw new InvalidOperationException("IAnnotationService not registered");

            _annotations = new ObservableCollection<AnnotationItem>();
            AnnotationListView.ItemsSource = _annotations;

            // 初始化系统预设类别
            _systemCategories = new HashSet<string>
            {
                "病灶区域", "正常组织", "骨折", "肿瘤", "血管", "其他"
            };

            // 默认选择矩形工具
            RectangleToolButton.IsChecked = true;
            _currentAnnotationTool = "Rectangle";

            // 设置默认类别
            AnnotationCategoryComboBox.SelectedIndex = 0;

            // 订阅窗宽窗位变化事件
            WindowLevelControl.WindowLevelChanged += OnWindowLevelChanged;

            // 初始化类别列表
            RefreshCategoryList();

            // 延迟初始化置信度阈值，避免与XAML初始化冲突
            Loaded += AnnotationView_Loaded;
        }

        /// <summary>
        /// 页面加载完成事件处理
        /// </summary>
        private void AnnotationView_Loaded(object sender, RoutedEventArgs e)
        {
            // 初始化置信度阈值，确保不低于0.1
            _currentConfidenceThreshold = Math.Max(ConfidenceThresholdSlider.Value, 0.1);
            if (ConfidenceThresholdSlider.Value < 0.1)
            {
                // 临时移除事件处理程序以避免在初始化时触发事件
                ConfidenceThresholdSlider.ValueChanged -= ConfidenceThresholdSlider_ValueChanged;
                ConfidenceThresholdSlider.Value = 0.1;
                ConfidenceThresholdSlider.ValueChanged += ConfidenceThresholdSlider_ValueChanged;
            }
        }

        /// <summary>
        /// 打开图像文件
        /// </summary>
        private void OpenImage_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择图像文件",
                Filter = "图像文件 (*.png;*.jpg;*.jpeg;*.bmp;*.dcm)|*.png;*.jpg;*.jpeg;*.bmp;*.dcm|" +
                        "PNG 文件 (*.png)|*.png|" +
                        "JPEG 文件 (*.jpg;*.jpeg)|*.jpg;*.jpeg|" +
                        "BMP 文件 (*.bmp)|*.bmp|" +
                        "DICOM 文件 (*.dcm)|*.dcm|" +
                        "所有文件 (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    LoadImage(openFileDialog.FileName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加载图像失败: {FileName}", openFileDialog.FileName);
                    MessageBox.Show($"加载图像失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 加载图像文件
        /// </summary>
        private async void LoadImage(string filePath)
        {
            try
            {
                var fileInfo = new System.IO.FileInfo(filePath);

                if (fileInfo.Extension.ToLower() == ".dcm")
                {
                    // 使用DICOM服务处理DICOM文件
                    await LoadDicomImageAsync(filePath);
                    return;
                }

                // 加载标准图像格式
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(filePath);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();
                bitmap.Freeze();

                _currentImage = bitmap;
                _isDicomImage = false;
                _currentDicomFilePath = null;

                AnnotationImage.Source = _currentImage;
                AnnotationImage.Visibility = Visibility.Visible;
                AnnotationCanvas.Visibility = Visibility.Visible;
                AnnotationPlaceholderPanel.Visibility = Visibility.Collapsed;
                AnnotationStatusPanel.Visibility = Visibility.Visible;

                // 设置画布大小与图像匹配
                AnnotationCanvas.Width = bitmap.PixelWidth;
                AnnotationCanvas.Height = bitmap.PixelHeight;

                _hasImage = true;
                UpdateStatus("图像加载完成，可以开始标注");

                // 隐藏窗宽窗位控制（仅对DICOM文件显示）
                WindowLevelControl.Visibility = Visibility.Collapsed;

                _logger.LogInformation("图像加载成功: {FileName}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载图像时发生错误: {FileName}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 加载DICOM图像文件
        /// </summary>
        private async Task LoadDicomImageAsync(string filePath)
        {
            try
            {
                _logger.LogInformation("开始加载DICOM文件: {FilePath}", filePath);

                // 使用DICOM服务解析文件
                var dicomInstance = await _gdcmDicomService.ParseDicomFileAsync(filePath);

                // 提取图像
                var bitmapSource = await _gdcmDicomService.ExtractImageAsync(filePath);

                if (bitmapSource != null)
                {
                    _currentImage = bitmapSource;
                    _isDicomImage = true;
                    _currentDicomFilePath = filePath;

                    AnnotationImage.Source = _currentImage;
                    AnnotationImage.Visibility = Visibility.Visible;
                    AnnotationCanvas.Visibility = Visibility.Visible;
                    AnnotationPlaceholderPanel.Visibility = Visibility.Collapsed;
                    AnnotationStatusPanel.Visibility = Visibility.Visible;

                    // 设置画布大小与图像匹配
                    AnnotationCanvas.Width = dicomInstance.Columns;
                    AnnotationCanvas.Height = dicomInstance.Rows;

                    _hasImage = true;
                    UpdateStatus($"DICOM图像加载完成，SOP实例UID: {dicomInstance.SopInstanceUid}，可以开始标注");

                    // 显示窗宽窗位控制并设置原始值
                    WindowLevelControl.Visibility = Visibility.Visible;
                    WindowLevelControl.SetOriginalValues(dicomInstance.WindowWidth, dicomInstance.WindowCenter);

                    _logger.LogInformation("DICOM图像加载成功: {FilePath}", filePath);
                }
                else
                {
                    throw new InvalidOperationException("无法从DICOM文件中提取图像数据");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载DICOM图像时发生错误: {FilePath}", filePath);
                MessageBox.Show($"加载DICOM文件失败: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 置信度阈值滑块变化事件
        /// </summary>
        private void ConfidenceThresholdSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            // 防止递归调用
            if (sender is Slider slider && Math.Abs(slider.Value - e.NewValue) < 0.001)
                return;

            _currentConfidenceThreshold = e.NewValue;

            // 确保值不低于0.1
            if (_currentConfidenceThreshold < 0.1)
            {
                _currentConfidenceThreshold = 0.1;
                // 临时移除事件处理程序以避免递归
                ConfidenceThresholdSlider.ValueChanged -= ConfidenceThresholdSlider_ValueChanged;
                ConfidenceThresholdSlider.Value = 0.1;
                ConfidenceThresholdSlider.ValueChanged += ConfidenceThresholdSlider_ValueChanged;
                return;
            }

            // 确保值为0.1的倍数
            var roundedValue = Math.Round(_currentConfidenceThreshold, 1);
            if (Math.Abs(_currentConfidenceThreshold - roundedValue) > 0.01)
            {
                _currentConfidenceThreshold = roundedValue;
                // 临时移除事件处理程序以避免递归
                ConfidenceThresholdSlider.ValueChanged -= ConfidenceThresholdSlider_ValueChanged;
                ConfidenceThresholdSlider.Value = roundedValue;
                ConfidenceThresholdSlider.ValueChanged += ConfidenceThresholdSlider_ValueChanged;
                return;
            }

            _logger.LogInformation("置信度阈值已更改为: {Threshold:F1}", _currentConfidenceThreshold);
            UpdateStatus($"置信度阈值设置为: {_currentConfidenceThreshold:F1}");

            // 如果有AI检测结果，重新过滤显示
            FilterAnnotationsByConfidence();
        }

        /// <summary>
        /// 根据置信度阈值过滤标注显示
        /// </summary>
        private void FilterAnnotationsByConfidence()
        {
            try
            {
                foreach (var annotation in _annotations)
                {
                    if (annotation.Shape != null && annotation.Category.Contains("AI检测") || annotation.Category == "病灶区域")
                    {
                        // 对于AI检测的标注，根据置信度决定是否显示
                        if (annotation.Confidence >= _currentConfidenceThreshold)
                        {
                            if (annotation.Shape != null && !AnnotationCanvas.Children.Contains(annotation.Shape))
                            {
                                AnnotationCanvas.Children.Add(annotation.Shape);
                            }
                            if (annotation.Shape != null)
                            {
                                annotation.Shape.Visibility = Visibility.Visible;
                            }
                        }
                        else
                        {
                            if (annotation.Shape != null)
                            {
                                annotation.Shape.Visibility = Visibility.Collapsed;
                            }
                        }
                    }
                }

                // 更新标注数量显示
                var visibleCount = _annotations.Count(a => a.Shape?.Visibility == Visibility.Visible);
                var totalCount = _annotations.Count;
                AnnotationCountText.Text = $"标注数量: {visibleCount}/{totalCount} (显示/总计)";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "过滤标注时发生错误");
            }
        }

        /// <summary>
        /// 快捷置信度设置按钮点击事件
        /// </summary>
        private void QuickConfidence_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string confidenceStr)
            {
                if (double.TryParse(confidenceStr, out double confidence))
                {
                    ConfidenceThresholdSlider.Value = confidence;
                    _logger.LogInformation("快捷设置置信度阈值为: {Confidence:F1}", confidence);
                }
            }
        }

        /// <summary>
        /// 窗宽窗位变化事件处理
        /// </summary>
        private async void OnWindowLevelChanged(object? sender, WindowLevelChangedEventArgs e)
        {
            if (!_isDicomImage || string.IsNullOrEmpty(_currentDicomFilePath))
                return;

            try
            {
                _logger.LogInformation("应用窗宽窗位: 窗宽={WindowWidth}, 窗位={WindowCenter}", e.WindowWidth, e.WindowCenter);

                // 使用GDCM图像处理器应用窗宽窗位
                var adjustedImage = await _gdcmImageProcessor.ApplyWindowLevelAsync(
                    _currentDicomFilePath, e.WindowCenter, e.WindowWidth);

                if (adjustedImage != null)
                {
                    _currentImage = adjustedImage;
                    AnnotationImage.Source = _currentImage;
                    _logger.LogInformation("窗宽窗位应用成功");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "应用窗宽窗位失败");
                MessageBox.Show($"应用窗宽窗位失败: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 标注工具选择
        /// </summary>
        private void AnnotationTool_Click(object sender, RoutedEventArgs e)
        {
            if (sender is ToggleButton button)
            {
                // 取消其他工具的选择
                RectangleToolButton.IsChecked = false;
                CircleToolButton.IsChecked = false;
                PolygonToolButton.IsChecked = false;
                PointToolButton.IsChecked = false;

                // 选择当前工具
                button.IsChecked = true;
                _currentAnnotationTool = button.Tag?.ToString() ?? "";

                _logger.LogInformation("选择标注工具: {Tool}", _currentAnnotationTool);

                var toolName = GetToolDisplayName(_currentAnnotationTool);
                var colorName = GetAnnotationColorName();
                UpdateStatus($"已选择 {toolName} 工具 - 将使用{colorName}进行标注");
            }
        }

        /// <summary>
        /// 获取工具显示名称
        /// </summary>
        private string GetToolDisplayName(string toolName)
        {
            return toolName switch
            {
                "Rectangle" => "矩形",
                "Circle" => "圆形",
                "Polygon" => "多边形",
                "Point" => "点",
                _ => "未知"
            };
        }

        /// <summary>
        /// 画布鼠标按下事件
        /// </summary>
        private void AnnotationCanvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            _logger.LogInformation("鼠标按下事件触发 - HasImage: {HasImage}, Tool: {Tool}", _hasImage, _currentAnnotationTool);

            if (!_hasImage)
            {
                UpdateStatus("请先加载图像");
                return;
            }

            if (string.IsNullOrEmpty(_currentAnnotationTool))
            {
                UpdateStatus("请先选择标注工具");
                return;
            }

            _startPoint = e.GetPosition(AnnotationCanvas);
            _isDrawing = true;

            _logger.LogInformation("开始绘制 {Tool} 在位置 ({X}, {Y})", _currentAnnotationTool, _startPoint.X, _startPoint.Y);

            switch (_currentAnnotationTool)
            {
                case "Rectangle":
                    StartDrawingRectangle(_startPoint);
                    break;
                case "Circle":
                    StartDrawingCircle(_startPoint);
                    break;
                case "Point":
                    CreatePoint(_startPoint);
                    break;
                default:
                    UpdateStatus($"未知的标注工具: {_currentAnnotationTool}");
                    return;
            }

            AnnotationCanvas.CaptureMouse();
            UpdateStatus($"正在绘制{GetToolDisplayName(_currentAnnotationTool)}...");
        }

        /// <summary>
        /// 画布鼠标移动事件
        /// </summary>
        private void AnnotationCanvas_MouseMove(object sender, System.Windows.Input.MouseEventArgs e)
        {
            if (!_isDrawing || _currentShape == null) return;

            var currentPoint = e.GetPosition(AnnotationCanvas);

            switch (_currentAnnotationTool)
            {
                case "Rectangle":
                    UpdateRectangle(currentPoint);
                    break;
                case "Circle":
                    UpdateCircle(currentPoint);
                    break;
            }
        }

        /// <summary>
        /// 画布鼠标释放事件
        /// </summary>
        private void AnnotationCanvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (!_isDrawing) return;

            _isDrawing = false;
            AnnotationCanvas.ReleaseMouseCapture();

            if (_currentShape != null)
            {
                // 完成标注
                CompleteAnnotation();
            }
        }

        /// <summary>
        /// 开始绘制矩形
        /// </summary>
        private void StartDrawingRectangle(System.Windows.Point startPoint)
        {
            var rectangle = new Rectangle
            {
                Stroke = GetAnnotationColor(),
                StrokeThickness = 2,
                Fill = Brushes.Transparent
            };

            Canvas.SetLeft(rectangle, startPoint.X);
            Canvas.SetTop(rectangle, startPoint.Y);

            AnnotationCanvas.Children.Add(rectangle);
            _currentShape = rectangle;
        }

        /// <summary>
        /// 更新矩形
        /// </summary>
        private void UpdateRectangle(System.Windows.Point currentPoint)
        {
            if (_currentShape is Rectangle rectangle)
            {
                var width = Math.Abs(currentPoint.X - _startPoint.X);
                var height = Math.Abs(currentPoint.Y - _startPoint.Y);

                rectangle.Width = width;
                rectangle.Height = height;

                Canvas.SetLeft(rectangle, Math.Min(_startPoint.X, currentPoint.X));
                Canvas.SetTop(rectangle, Math.Min(_startPoint.Y, currentPoint.Y));
            }
        }

        /// <summary>
        /// 开始绘制圆形
        /// </summary>
        private void StartDrawingCircle(System.Windows.Point startPoint)
        {
            var ellipse = new Ellipse
            {
                Stroke = GetAnnotationColor(),
                StrokeThickness = 2,
                Fill = Brushes.Transparent
            };

            Canvas.SetLeft(ellipse, startPoint.X);
            Canvas.SetTop(ellipse, startPoint.Y);

            AnnotationCanvas.Children.Add(ellipse);
            _currentShape = ellipse;
        }

        /// <summary>
        /// 更新圆形
        /// </summary>
        private void UpdateCircle(System.Windows.Point currentPoint)
        {
            if (_currentShape is Ellipse ellipse)
            {
                var radius = Math.Sqrt(Math.Pow(currentPoint.X - _startPoint.X, 2) +
                                     Math.Pow(currentPoint.Y - _startPoint.Y, 2));

                ellipse.Width = radius * 2;
                ellipse.Height = radius * 2;

                Canvas.SetLeft(ellipse, _startPoint.X - radius);
                Canvas.SetTop(ellipse, _startPoint.Y - radius);
            }
        }

        /// <summary>
        /// 创建点标注
        /// </summary>
        private void CreatePoint(System.Windows.Point point)
        {
            var annotationColor = GetAnnotationColor();
            var ellipse = new Ellipse
            {
                Width = 8,
                Height = 8,
                Fill = annotationColor,
                Stroke = GetDarkerBrush(annotationColor),
                StrokeThickness = 1
            };

            Canvas.SetLeft(ellipse, point.X - 4);
            Canvas.SetTop(ellipse, point.Y - 4);

            AnnotationCanvas.Children.Add(ellipse);
            _currentShape = ellipse;

            // 点标注立即完成
            CompleteAnnotation();
        }

        /// <summary>
        /// 完成标注
        /// </summary>
        private void CompleteAnnotation()
        {
            if (_currentShape == null) return;

            var category = AnnotationCategoryComboBox.Text;
            if (string.IsNullOrEmpty(category))
            {
                category = "未分类";
            }

            var annotation = new AnnotationItem
            {
                Id = Guid.NewGuid(),
                Type = GetToolDisplayName(_currentAnnotationTool),
                Category = category,
                Confidence = 1.0, // 手动标注置信度为1.0
                Shape = _currentShape,
                CreatedTime = DateTime.Now
            };

            _annotations.Add(annotation);
            UpdateAnnotationCount();

            var colorName = GetAnnotationColorName();
            UpdateStatus($"已添加 {colorName}{annotation.Type} 标注 - {annotation.Category}");

            _currentShape = null;
        }

        /// <summary>
        /// AI检测
        /// </summary>
        private async void AIDetection_Click(object sender, RoutedEventArgs e)
        {
            if (!_hasImage)
            {
                MessageBox.Show("请先加载图像。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                UpdateStatus("正在进行AI检测...");

                // 模拟AI检测过程
                await Task.Delay(2000);

                // 添加模拟的AI检测结果
                var detectedLesions = CreateSimulatedDetections();

                // 根据置信度阈值过滤结果
                var filteredLesions = detectedLesions.Where(l => l.Confidence >= _currentConfidenceThreshold).ToList();

                foreach (var lesion in filteredLesions)
                {
                    var aiAnnotation = new AnnotationItem
                    {
                        Id = Guid.NewGuid(),
                        Type = "矩形",
                        Category = "AI检测-病灶",
                        Confidence = lesion.Confidence,
                        Shape = lesion.Shape,
                        CreatedTime = DateTime.Now
                    };
                    _annotations.Add(aiAnnotation);
                }

                // 隐藏低置信度的检测结果
                var hiddenCount = detectedLesions.Count - filteredLesions.Count;
                foreach (var lesion in detectedLesions.Where(l => l.Confidence < _currentConfidenceThreshold))
                {
                    if (lesion.Shape != null)
                    {
                        lesion.Shape.Visibility = Visibility.Collapsed;
                    }
                }

                UpdateAnnotationCount();

                var statusMessage = $"AI检测完成，发现 {detectedLesions.Count} 个病灶";
                if (hiddenCount > 0)
                {
                    statusMessage += $"，其中 {hiddenCount} 个低置信度结果已隐藏";
                }
                UpdateStatus(statusMessage);

                var resultMessage = $"AI检测完成！\n" +
                                  $"总共发现 {detectedLesions.Count} 个可疑病灶区域\n" +
                                  $"显示 {filteredLesions.Count} 个高置信度结果（≥{_currentConfidenceThreshold:F1}）\n";

                if (hiddenCount > 0)
                {
                    resultMessage += $"隐藏 {hiddenCount} 个低置信度结果\n";
                }

                resultMessage += "已用绿色虚线矩形框标记。";

                MessageBox.Show(resultMessage, "AI检测结果", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI检测时发生错误");
                MessageBox.Show($"AI检测失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                UpdateStatus("AI检测失败");
            }
        }

        /// <summary>
        /// 创建模拟的AI检测结果
        /// </summary>
        private List<(Shape Shape, double Confidence)> CreateSimulatedDetections()
        {
            var detections = new List<(Shape Shape, double Confidence)>();
            var random = new Random();

            // 模拟检测到2-4个病灶
            int lesionCount = random.Next(2, 5);

            for (int i = 0; i < lesionCount; i++)
            {
                var rectangle = new Rectangle
                {
                    Width = random.Next(60, 120),
                    Height = random.Next(50, 100),
                    Stroke = Brushes.Green, // 使用绿色表示病灶
                    StrokeThickness = 3,
                    Fill = Brushes.Transparent,
                    StrokeDashArray = new DoubleCollection { 8, 4 } // 虚线表示AI检测
                };

                // 随机位置（确保在画布范围内）
                var canvasWidth = AnnotationCanvas.ActualWidth > 0 ? AnnotationCanvas.ActualWidth : 800;
                var canvasHeight = AnnotationCanvas.ActualHeight > 0 ? AnnotationCanvas.ActualHeight : 600;

                var x = random.Next(20, (int)canvasWidth - (int)rectangle.Width - 20);
                var y = random.Next(20, (int)canvasHeight - (int)rectangle.Height - 20);

                Canvas.SetLeft(rectangle, x);
                Canvas.SetTop(rectangle, y);

                AnnotationCanvas.Children.Add(rectangle);

                // 生成符合0.1倍数的随机置信度
                var confidenceOptions = new[] { 0.5, 0.6, 0.7, 0.8, 0.9, 1.0 };
                double confidence = confidenceOptions[random.Next(confidenceOptions.Length)];
                detections.Add((rectangle, confidence));
            }

            return detections;
        }

        /// <summary>
        /// 清除所有标注
        /// </summary>
        private void ClearAnnotations_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要清除所有标注吗？", "确认",
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                AnnotationCanvas.Children.Clear();
                _annotations.Clear();
                UpdateAnnotationCount();
                UpdateStatus("已清除所有标注");
            }
        }

        /// <summary>
        /// 添加自定义类别
        /// </summary>
        private void AddCategory_Click(object sender, RoutedEventArgs e)
        {
            var customCategory = CustomCategoryTextBox.Text.Trim();
            if (string.IsNullOrEmpty(customCategory))
            {
                MessageBox.Show("请输入类别名称", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 检查是否已存在
            foreach (ComboBoxItem item in AnnotationCategoryComboBox.Items)
            {
                if (item.Content.ToString() == customCategory)
                {
                    MessageBox.Show($"类别 '{customCategory}' 已存在", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
            }

            var newItem = new ComboBoxItem { Content = customCategory };
            AnnotationCategoryComboBox.Items.Add(newItem);
            AnnotationCategoryComboBox.SelectedItem = newItem;
            CustomCategoryTextBox.Clear();

            _logger.LogInformation("添加自定义类别: {Category}", customCategory);
            UpdateStatus($"已添加类别：{customCategory}");

            // 刷新类别列表
            RefreshCategoryList();
        }

        /// <summary>
        /// 删除选中的类别
        /// </summary>
        private void RemoveCategory_Click(object sender, RoutedEventArgs e)
        {
            if (AnnotationCategoryComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var categoryName = selectedItem.Content.ToString();

                // 检查是否为系统预设类别
                if (!string.IsNullOrEmpty(categoryName) && _systemCategories.Contains(categoryName))
                {
                    MessageBox.Show($"无法删除系统预设类别 '{categoryName}'", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 检查是否有使用该类别的标注
                var annotationsWithCategory = _annotations.Where(a => a.Category == categoryName).ToList();
                if (annotationsWithCategory.Any())
                {
                    var result = MessageBox.Show(
                        $"类别 '{categoryName}' 正在被 {annotationsWithCategory.Count} 个标注使用。\n删除类别将同时删除这些标注，是否继续？",
                        "确认删除",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // 删除使用该类别的标注
                        foreach (var annotation in annotationsWithCategory)
                        {
                            if (annotation.Shape != null)
                            {
                                AnnotationCanvas.Children.Remove(annotation.Shape);
                            }
                            _annotations.Remove(annotation);
                        }
                        UpdateAnnotationCount();
                    }
                    else
                    {
                        return;
                    }
                }

                // 删除类别
                AnnotationCategoryComboBox.Items.Remove(selectedItem);

                // 选择第一个类别
                if (AnnotationCategoryComboBox.Items.Count > 0)
                {
                    AnnotationCategoryComboBox.SelectedIndex = 0;
                }

                _logger.LogInformation("删除自定义类别: {Category}", categoryName);
                UpdateStatus($"已删除类别：{categoryName}");

                // 刷新类别列表
                RefreshCategoryList();
            }
            else
            {
                MessageBox.Show("请先选择要删除的类别", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// 标注列表选择变化
        /// </summary>
        private void AnnotationListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (AnnotationListView.SelectedItem is AnnotationItem annotation)
            {
                // 高亮显示选中的标注
                HighlightAnnotation(annotation);
            }
        }

        /// <summary>
        /// 高亮显示标注
        /// </summary>
        private void HighlightAnnotation(AnnotationItem annotation)
        {
            // 重置所有标注的样式
            foreach (var child in AnnotationCanvas.Children.OfType<Shape>())
            {
                if (child.StrokeThickness == 4) // 之前被高亮的
                {
                    child.StrokeThickness = 2;
                }
            }

            // 高亮当前选中的标注
            if (annotation.Shape != null)
            {
                annotation.Shape.StrokeThickness = 4;
            }
        }

        /// <summary>
        /// 编辑标注
        /// </summary>
        private void EditAnnotation_Click(object sender, RoutedEventArgs e)
        {
            if (AnnotationListView.SelectedItem is AnnotationItem annotation)
            {
                // 这里可以打开编辑对话框
                MessageBox.Show($"编辑标注功能正在开发中。\n当前选中：{annotation.Type} - {annotation.Category}",
                              "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("请先选择要编辑的标注。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 删除标注
        /// </summary>
        private void DeleteAnnotation_Click(object sender, RoutedEventArgs e)
        {
            if (AnnotationListView.SelectedItem is AnnotationItem annotation)
            {
                var result = MessageBox.Show($"确定要删除标注\"{annotation.Category}\"吗？", "确认",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    // 从画布移除形状
                    if (annotation.Shape != null)
                    {
                        AnnotationCanvas.Children.Remove(annotation.Shape);
                    }

                    // 从列表移除
                    _annotations.Remove(annotation);
                    UpdateAnnotationCount();
                    UpdateStatus("已删除标注");
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的标注。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 导出标注数据
        /// </summary>
        private void ExportAnnotations_Click(object sender, RoutedEventArgs e)
        {
            if (!_annotations.Any())
            {
                MessageBox.Show("没有可导出的标注数据。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var saveFileDialog = new SaveFileDialog
            {
                Title = "导出标注数据",
                Filter = "JSON 文件 (*.json)|*.json|" +
                        "文本文件 (*.txt)|*.txt|" +
                        "所有文件 (*.*)|*.*",
                DefaultExt = "json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ExportAnnotationsToFile(saveFileDialog.FileName);
                    MessageBox.Show("标注数据导出成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "导出标注数据失败");
                    MessageBox.Show($"导出失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 导出标注数据到文件
        /// </summary>
        private void ExportAnnotationsToFile(string filePath)
        {
            var exportData = _annotations.Select(a => new
            {
                Id = a.Id,
                Type = a.Type,
                Category = a.Category,
                Confidence = a.Confidence,
                CreatedTime = a.CreatedTime,
                // 简化的位置信息
                Position = GetShapePosition(a.Shape)
            }).ToList();

            var json = System.Text.Json.JsonSerializer.Serialize(exportData, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            File.WriteAllText(filePath, json);
        }

        /// <summary>
        /// 获取形状位置信息
        /// </summary>
        private object GetShapePosition(Shape? shape)
        {
            if (shape == null) return new { };

            var left = Canvas.GetLeft(shape);
            var top = Canvas.GetTop(shape);

            return shape switch
            {
                Rectangle rect => new { Left = left, Top = top, Width = rect.Width, Height = rect.Height },
                Ellipse ellipse => new { Left = left, Top = top, Width = ellipse.Width, Height = ellipse.Height },
                _ => new { Left = left, Top = top }
            };
        }

        /// <summary>
        /// 保存标注
        /// </summary>
        private void SaveAnnotations_Click(object sender, RoutedEventArgs e)
        {
            if (!_annotations.Any())
            {
                MessageBox.Show("没有可保存的标注数据。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var saveFileDialog = new SaveFileDialog
            {
                Title = "保存标注文件",
                Filter = "标注文件 (*.ann)|*.ann|JSON 文件 (*.json)|*.json",
                DefaultExt = "ann"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    ExportAnnotationsToFile(saveFileDialog.FileName);
                    MessageBox.Show("标注文件保存成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "保存标注文件失败");
                    MessageBox.Show($"保存失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 加载标注
        /// </summary>
        private void LoadAnnotations_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "加载标注文件",
                Filter = "标注文件 (*.ann)|*.ann|JSON 文件 (*.json)|*.json|所有文件 (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    LoadAnnotationsFromFile(openFileDialog.FileName);
                    MessageBox.Show("标注文件加载成功！", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加载标注文件失败");
                    MessageBox.Show($"加载失败：{ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 从文件加载标注数据
        /// </summary>
        private void LoadAnnotationsFromFile(string filePath)
        {
            try
            {
                // 检查文件格式
                var extension = System.IO.Path.GetExtension(filePath).ToLower();
                if (extension == ".json")
                {
                    // 尝试加载JSON格式的标注文件
                    var jsonContent = File.ReadAllText(filePath);
                    // 这里可以添加JSON解析逻辑
                    MessageBox.Show($"标注文件加载成功: {System.IO.Path.GetFileName(filePath)}", "成功",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("目前仅支持JSON格式的标注文件。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载标注文件失败: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        private void UpdateStatus(string status)
        {
            AnnotationStatusText.Text = status;
        }

        /// <summary>
        /// 更新标注数量
        /// </summary>
        private void UpdateAnnotationCount()
        {
            AnnotationCountText.Text = $"标注数量: {_annotations.Count}";
        }

        /// <summary>
        /// 根据标注类别获取颜色
        /// </summary>
        private Brush GetAnnotationColor()
        {
            var category = AnnotationCategoryComboBox.Text?.Trim() ?? "";

            return category.ToLowerInvariant() switch
            {
                "病灶区域" or "病灶" or "lesion" => Brushes.Green,
                "肿瘤" or "tumor" => Brushes.Red,
                "骨折" or "fracture" => Brushes.Orange,
                "血管" or "vessel" => Brushes.Blue,
                "正常组织" or "normal" => Brushes.LightBlue,
                _ => Brushes.Yellow // 默认颜色
            };
        }

        /// <summary>
        /// 根据标注类别获取颜色名称（用于显示）
        /// </summary>
        private string GetAnnotationColorName()
        {
            var category = AnnotationCategoryComboBox.Text?.Trim() ?? "";

            return category.ToLowerInvariant() switch
            {
                "病灶区域" or "病灶" or "lesion" => "绿色",
                "肿瘤" or "tumor" => "红色",
                "骨折" or "fracture" => "橙色",
                "血管" or "vessel" => "蓝色",
                "正常组织" or "normal" => "浅蓝色",
                _ => "黄色"
            };
        }

        /// <summary>
        /// 获取更深的颜色画刷（用于边框）
        /// </summary>
        private Brush GetDarkerBrush(Brush brush)
        {
            return brush switch
            {
                _ when brush == Brushes.Green => Brushes.DarkGreen,
                _ when brush == Brushes.Red => Brushes.DarkRed,
                _ when brush == Brushes.Orange => Brushes.DarkOrange,
                _ when brush == Brushes.Blue => Brushes.DarkBlue,
                _ when brush == Brushes.LightBlue => Brushes.Blue,
                _ when brush == Brushes.Yellow => Brushes.Orange,
                _ => Brushes.Black
            };
        }

        /// <summary>
        /// 刷新类别列表
        /// </summary>
        private void RefreshCategoryList_Click(object sender, RoutedEventArgs e)
        {
            RefreshCategoryList();
        }

        /// <summary>
        /// 类别列表选择变化
        /// </summary>
        private void CategoryListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CategoryListBox.SelectedItem is CategoryInfo categoryInfo)
            {
                // 在主下拉框中选择对应的类别
                foreach (ComboBoxItem item in AnnotationCategoryComboBox.Items)
                {
                    if (item.Content.ToString() == categoryInfo.Name)
                    {
                        AnnotationCategoryComboBox.SelectedItem = item;
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 刷新类别列表显示
        /// </summary>
        private void RefreshCategoryList()
        {
            var categoryInfos = new List<CategoryInfo>();

            foreach (ComboBoxItem item in AnnotationCategoryComboBox.Items)
            {
                var categoryName = item.Content?.ToString() ?? "";
                var isSystem = !string.IsNullOrEmpty(categoryName) && _systemCategories.Contains(categoryName);
                var color = GetCategoryColor(categoryName);
                var borderColor = GetCategoryBorderColor(categoryName);

                categoryInfos.Add(new CategoryInfo
                {
                    Name = categoryName,
                    IsSystemCategory = isSystem,
                    TypeLabel = isSystem ? "系统" : "自定义",
                    ColorIndicator = color,
                    BorderColor = borderColor
                });
            }

            CategoryListBox.ItemsSource = categoryInfos;
        }

        /// <summary>
        /// 获取类别颜色
        /// </summary>
        private Color GetCategoryColor(string category)
        {
            return category.ToLowerInvariant() switch
            {
                "病灶区域" or "病灶" or "lesion" => Colors.Green,
                "肿瘤" or "tumor" => Colors.Red,
                "骨折" or "fracture" => Colors.Orange,
                "血管" or "vessel" => Colors.Blue,
                "正常组织" or "normal" => Colors.LightBlue,
                _ => Colors.Yellow
            };
        }

        /// <summary>
        /// 获取类别边框颜色
        /// </summary>
        private Color GetCategoryBorderColor(string category)
        {
            return category.ToLowerInvariant() switch
            {
                "病灶区域" or "病灶" or "lesion" => Colors.DarkGreen,
                "肿瘤" or "tumor" => Colors.DarkRed,
                "骨折" or "fracture" => Colors.DarkOrange,
                "血管" or "vessel" => Colors.DarkBlue,
                "正常组织" or "normal" => Colors.Blue,
                _ => Colors.Orange
            };
        }

        /// <summary>
        /// AI批量标注按钮点击事件
        /// </summary>
        private async void BatchAIAnnotation_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 选择源文件夹
                var folderDialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "选择包含DICOM文件的文件夹",
                    ShowNewFolderButton = false
                };

                if (folderDialog.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return;
                }

                var sourceFolder = folderDialog.SelectedPath;

                // 选择输出文件夹
                var outputFolderDialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "选择AI标注结果输出文件夹",
                    ShowNewFolderButton = true
                };

                if (outputFolderDialog.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return;
                }

                var outputFolder = outputFolderDialog.SelectedPath;

                // 确保输出文件夹存在
                if (!Directory.Exists(outputFolder))
                {
                    Directory.CreateDirectory(outputFolder);
                }

                // 创建智能AI打标文件夹
                var aiAnnotationFolder = System.IO.Path.Combine(outputFolder, "智能AI打标");
                if (!Directory.Exists(aiAnnotationFolder))
                {
                    Directory.CreateDirectory(aiAnnotationFolder);
                }

                // 查找所有DICOM文件
                var dicomFiles = Directory.GetFiles(sourceFolder, "*.dcm", SearchOption.AllDirectories).ToList();

                if (!dicomFiles.Any())
                {
                    MessageBox.Show("在选定文件夹中未找到DICOM文件（.dcm）。", "提示",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 显示确认对话框
                var confirmResult = MessageBox.Show(
                    $"找到 {dicomFiles.Count} 个DICOM文件。\n\n" +
                    $"源文件夹: {sourceFolder}\n" +
                    $"输出文件夹: {aiAnnotationFolder}\n\n" +
                    "是否开始AI批量标注？",
                    "确认批量标注",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (confirmResult != MessageBoxResult.Yes)
                {
                    return;
                }

                // 开始批量处理
                await StartBatchAnnotationAsync(dicomFiles, aiAnnotationFolder);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AI批量标注失败");
                MessageBox.Show($"AI批量标注失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 开始批量标注处理
        /// </summary>
        private async Task StartBatchAnnotationAsync(List<string> dicomFiles, string outputFolder)
        {
            _batchProcessingCancellationTokenSource = new CancellationTokenSource();
            var cancellationToken = _batchProcessingCancellationTokenSource.Token;

            // 创建进度窗口
            var progressWindow = new BatchAnnotationProgressWindow();
            progressWindow.Owner = Window.GetWindow(this);
            progressWindow.TotalFiles = dicomFiles.Count;
            progressWindow.CancelRequested += () => _batchProcessingCancellationTokenSource?.Cancel();

            try
            {
                progressWindow.Show();

                var processedCount = 0;
                var successCount = 0;
                var failedFiles = new List<string>();
                var processedResults = new List<BatchDetectionResult>();

                foreach (var dicomFile in dicomFiles)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        break;
                    }

                    try
                    {
                        progressWindow.UpdateProgress(processedCount, System.IO.Path.GetFileName(dicomFile));

                        // 处理单个DICOM文件
                        var result = await ProcessSingleDicomFileAsync(dicomFile, outputFolder, cancellationToken);

                        // 收集处理结果用于汇总报告
                        var batchResult = new BatchDetectionResult
                        {
                            FilePath = dicomFile,
                            FileName = System.IO.Path.GetFileName(dicomFile),
                            Success = result.Success,
                            ErrorMessage = result.ErrorMessage,
                            DetectionCount = result.AnnotationCount,
                            ProcessingTime = DateTime.Now,
                            OutputJsonPath = result.OutputJsonPath,
                            OutputImagePath = result.OutputImagePath
                        };
                        processedResults.Add(batchResult);

                        if (result.Success)
                        {
                            successCount++;
                            progressWindow.AddSuccessFile(dicomFile, result.AnnotationCount);
                        }
                        else
                        {
                            failedFiles.Add(dicomFile);
                            progressWindow.AddFailedFile(dicomFile, result.ErrorMessage ?? "未知错误");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理DICOM文件失败: {FilePath}", dicomFile);
                        failedFiles.Add(dicomFile);
                        progressWindow.AddFailedFile(dicomFile, ex.Message);
                    }

                    processedCount++;
                    progressWindow.UpdateProgress(processedCount, "");
                }

                // 生成汇总报告
                await GenerateBatchDetectionSummaryReportAsync(dicomFiles, successCount, failedFiles, outputFolder, processedResults);

                // 显示完成结果
                var completionMessage = $"批量AI检测完成！\n\n" +
                                      $"总文件数: {dicomFiles.Count}\n" +
                                      $"成功处理: {successCount}\n" +
                                      $"失败: {failedFiles.Count}\n\n" +
                                      $"结果保存在: {outputFolder}\n" +
                                      $"汇总报告: 批量检测汇总报告.html";

                if (failedFiles.Any())
                {
                    completionMessage += $"\n\n失败的文件:\n{string.Join("\n", failedFiles.Take(5).Select(System.IO.Path.GetFileName))}";
                    if (failedFiles.Count > 5)
                    {
                        completionMessage += $"\n... 还有 {failedFiles.Count - 5} 个文件";
                    }
                }

                progressWindow.ShowCompletion(completionMessage);
            }
            catch (OperationCanceledException)
            {
                progressWindow.ShowCompletion("批量标注已取消。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量标注过程中发生错误");
                progressWindow.ShowCompletion($"批量标注失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 处理单个DICOM文件
        /// </summary>
        private async Task<BatchAnnotationFileResult> ProcessSingleDicomFileAsync(string dicomFilePath, string outputFolder, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理DICOM文件: {FilePath}", dicomFilePath);

                // 加载DICOM文件
                var dicomInstance = await _gdcmDicomService.ParseDicomFileAsync(dicomFilePath);
                if (dicomInstance == null)
                {
                    return new BatchAnnotationFileResult
                    {
                        Success = false,
                        ErrorMessage = "无法加载DICOM文件"
                    };
                }

                // 获取当前窗宽窗位设置
                var currentWindowWidth = dicomInstance.WindowWidth;
                var currentWindowCenter = dicomInstance.WindowCenter;

                // 创建智能标注配置
                var annotationConfig = new SmartAnnotationConfig
                {
                    EnableMultiModelFusion = true,
                    EnableQualityAssessment = true,
                    EnableSmartFiltering = true,
                    FilterConfig = new SmartFilterConfig
                    {
                        MinConfidenceThreshold = 0.25, // 进一步降低阈值确保模拟检测结果不被过滤
                        MinQualityThreshold = 0.25     // 进一步降低质量阈值以适应模拟数据
                    },
                    // 添加默认的模型配置
                    ModelConfigs = new List<AutoAnnotationConfig>
                    {
                        new AutoAnnotationConfig
                        {
                            ModelPath = "mock_detection_model", // 使用模拟模型进行演示
                            ConfidenceThreshold = 0.25, // 与FilterConfig保持一致
                            IouThreshold = 0.45,
                            TargetClasses = new List<string>(),
                            MinBoundingBoxSize = (10, 10),
                            MaxBoundingBoxSize = (1000, 1000),
                            EnablePostProcessing = true,
                            AutoAdjustWindowLevel = true,
                            PreprocessingOptions = new ImagePreprocessingOptions
                            {
                                Normalize = true,
                                NormalizationRange = (0.0, 1.0),
                                Resize = false,
                                HistogramEqualization = false,
                                ApplyClahe = true,
                                ClaheParameters = new ClaheParameters
                                {
                                    ClipLimit = 2.0,
                                    TileGridSize = (8, 8)
                                }
                            }
                        }
                    }
                };

                // 执行AI标注
                _logger.LogInformation("开始执行AI标注，文件: {FilePath}", dicomFilePath);
                var annotationResult = await _smartAnnotationService.GenerateSmartAnnotationsAsync(
                    dicomInstance, annotationConfig, cancellationToken);

                _logger.LogInformation("AI标注完成，成功: {Success}, 标注数量: {Count}, 错误: {Error}",
                    annotationResult.Success,
                    annotationResult.FinalAnnotations?.Count ?? 0,
                    annotationResult.ErrorMessage);

                if (!annotationResult.Success || annotationResult.FinalAnnotations?.Any() != true)
                {
                    var errorMessage = annotationResult.Success ? "AI标注未检测到任何目标" : annotationResult.ErrorMessage;
                    _logger.LogWarning("AI标注失败或无结果，文件: {FilePath}, 错误: {Error}", dicomFilePath, errorMessage);
                    return new BatchAnnotationFileResult
                    {
                        Success = false,
                        ErrorMessage = errorMessage
                    };
                }

                // 创建输出文件名
                var fileName = System.IO.Path.GetFileNameWithoutExtension(dicomFilePath);
                var outputFileName = $"{fileName}_AI标注";

                // 保存标注结果
                var annotationData = new BatchAnnotationResult
                {
                    SourceFile = dicomFilePath,
                    ProcessedTime = DateTime.Now,
                    WindowWidth = currentWindowWidth,
                    WindowCenter = currentWindowCenter,
                    Annotations = annotationResult.FinalAnnotations.Select(a => new AnnotationData
                    {
                        Id = a.Id.ToString(),
                        Type = a.Type.ToString(),
                        Label = a.Label,
                        Confidence = a.Confidence,
                        BoundingBox = new BoundingBoxData
                        {
                            X = a.BoundingBox.Left,
                            Y = a.BoundingBox.Top,
                            Width = a.BoundingBox.Width,
                            Height = a.BoundingBox.Height
                        },
                        WindowLevel = new WindowLevelData
                        {
                            WindowWidth = currentWindowWidth,
                            WindowCenter = currentWindowCenter
                        }
                    }).ToList()
                };

                // 保存JSON文件
                var jsonOutputPath = System.IO.Path.Combine(outputFolder, $"{outputFileName}.json");
                var jsonContent = JsonSerializer.Serialize(annotationData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                await File.WriteAllTextAsync(jsonOutputPath, jsonContent, cancellationToken);

                // 生成带标注的图像
                var imageOutputPath = System.IO.Path.Combine(outputFolder, $"{outputFileName}.png");
                await GenerateAnnotatedImageAsync(dicomFilePath, annotationResult.FinalAnnotations, imageOutputPath,
                                                currentWindowWidth, currentWindowCenter, cancellationToken);

                _logger.LogInformation("DICOM文件处理完成: {FilePath}, 标注数量: {Count}",
                                     dicomFilePath, annotationResult.FinalAnnotations.Count);

                return new BatchAnnotationFileResult
                {
                    Success = true,
                    AnnotationCount = annotationResult.FinalAnnotations.Count,
                    OutputJsonPath = jsonOutputPath,
                    OutputImagePath = imageOutputPath
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理DICOM文件时发生错误: {FilePath}", dicomFilePath);
                return new BatchAnnotationFileResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 生成带标注的图像
        /// </summary>
        private async Task GenerateAnnotatedImageAsync(string dicomFilePath, List<Annotation> annotations,
                                                     string outputPath, double windowWidth, double windowCenter,
                                                     CancellationToken cancellationToken)
        {
            try
            {
                // 应用窗宽窗位生成图像
                var image = await _gdcmImageProcessor.ApplyWindowLevelAsync(dicomFilePath, windowCenter, windowWidth);
                if (image == null)
                {
                    throw new InvalidOperationException("无法生成DICOM图像");
                }

                // 创建绘图上下文
                var drawingVisual = new DrawingVisual();
                using (var drawingContext = drawingVisual.RenderOpen())
                {
                    // 绘制原始图像
                    drawingContext.DrawImage(image, new Rect(0, 0, image.PixelWidth, image.PixelHeight));

                    // 绘制标注
                    foreach (var annotation in annotations)
                    {
                        DrawAnnotationOnContext(drawingContext, annotation, image.PixelWidth, image.PixelHeight);
                    }
                }

                // 渲染为位图
                var renderTargetBitmap = new RenderTargetBitmap(
                    image.PixelWidth, image.PixelHeight, 96, 96, PixelFormats.Pbgra32);
                renderTargetBitmap.Render(drawingVisual);

                // 保存为PNG
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderTargetBitmap));

                using (var fileStream = new FileStream(outputPath, FileMode.Create))
                {
                    encoder.Save(fileStream);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成带标注图像失败: {OutputPath}", outputPath);
                throw;
            }
        }

        /// <summary>
        /// 在绘图上下文中绘制标注
        /// </summary>
        private void DrawAnnotationOnContext(DrawingContext drawingContext, Annotation annotation, int imageWidth, int imageHeight)
        {
            var brush = new SolidColorBrush(Colors.Red) { Opacity = 0.3 };
            var pen = new Pen(new SolidColorBrush(Colors.Red), 2);

            // 转换为绝对坐标
            var absoluteBox = annotation.BoundingBox.ToAbsolute(imageWidth, imageHeight);
            var rect = new Rect(absoluteBox.X, absoluteBox.Y, absoluteBox.Width, absoluteBox.Height);

            // 绘制边界框
            drawingContext.DrawRectangle(brush, pen, rect);

            // 绘制标签
            var labelText = $"{annotation.Label} ({annotation.Confidence:F2})";
            var formattedText = new FormattedText(labelText,
                System.Globalization.CultureInfo.CurrentCulture,
                FlowDirection.LeftToRight,
                new Typeface("Arial"),
                12,
                new SolidColorBrush(Colors.Red),
                1.0);

            var labelBackground = new Rect(absoluteBox.X, absoluteBox.Y - 20, formattedText.Width + 4, 20);
            drawingContext.DrawRectangle(new SolidColorBrush(Colors.Red), null, labelBackground);
            drawingContext.DrawText(formattedText, new System.Windows.Point(absoluteBox.X + 2, absoluteBox.Y - 18));
        }

        /// <summary>
        /// 生成批量检测汇总报告
        /// </summary>
        private async Task GenerateBatchDetectionSummaryReportAsync(
            List<string> allFiles,
            int successCount,
            List<string> failedFiles,
            string outputFolder,
            List<BatchDetectionResult> results)
        {
            try
            {
                var reportPath = System.IO.Path.Combine(outputFolder, "批量检测汇总报告.html");
                var totalDetections = results.Where(r => r.Success).Sum(r => r.DetectionCount);
                var processingTime = DateTime.Now;

                var htmlContent = GenerateHtmlReport(allFiles, successCount, failedFiles, results, totalDetections, processingTime);
                await File.WriteAllTextAsync(reportPath, htmlContent, System.Text.Encoding.UTF8);

                // 同时生成CSV格式的详细报告
                var csvPath = System.IO.Path.Combine(outputFolder, "批量检测详细结果.csv");
                var csvContent = GenerateCsvReport(results);
                await File.WriteAllTextAsync(csvPath, csvContent, System.Text.Encoding.UTF8);

                _logger.LogInformation("批量检测汇总报告已生成: {ReportPath}", reportPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成汇总报告失败");
            }
        }

        /// <summary>
        /// 生成HTML格式的汇总报告
        /// </summary>
        private string GenerateHtmlReport(List<string> allFiles, int successCount, List<string> failedFiles,
            List<BatchDetectionResult> results, int totalDetections, DateTime processingTime)
        {
            var html = $@"
<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>批量AI检测汇总报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #007acc; }}
        .header h1 {{ color: #007acc; margin: 0; font-size: 28px; }}
        .header p {{ color: #666; margin: 10px 0 0 0; font-size: 16px; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .summary-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }}
        .summary-card.success {{ background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }}
        .summary-card.failed {{ background: linear-gradient(135deg, #f44336 0%, #da190b 100%); }}
        .summary-card.detections {{ background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }}
        .summary-card h3 {{ margin: 0 0 10px 0; font-size: 18px; }}
        .summary-card .number {{ font-size: 32px; font-weight: bold; margin: 10px 0; }}
        .section {{ margin-bottom: 30px; }}
        .section h2 {{ color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 15px; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #007acc; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        tr:hover {{ background-color: #f5f5f5; }}
        .status-success {{ color: #4CAF50; font-weight: bold; }}
        .status-failed {{ color: #f44336; font-weight: bold; }}
        .footer {{ text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🔍 批量AI检测汇总报告</h1>
            <p>生成时间: {processingTime:yyyy年MM月dd日 HH:mm:ss}</p>
        </div>

        <div class='summary'>
            <div class='summary-card'>
                <h3>总文件数</h3>
                <div class='number'>{allFiles.Count}</div>
            </div>
            <div class='summary-card success'>
                <h3>成功处理</h3>
                <div class='number'>{successCount}</div>
            </div>
            <div class='summary-card failed'>
                <h3>处理失败</h3>
                <div class='number'>{failedFiles.Count}</div>
            </div>
            <div class='summary-card detections'>
                <h3>总检测数</h3>
                <div class='number'>{totalDetections}</div>
            </div>
        </div>";

            // 添加成功处理的文件详情
            if (results.Any(r => r.Success))
            {
                html += @"
        <div class='section'>
            <h2>✅ 成功处理的文件</h2>
            <table>
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>检测数量</th>
                        <th>处理时间</th>
                        <th>输出文件</th>
                    </tr>
                </thead>
                <tbody>";

                foreach (var result in results.Where(r => r.Success))
                {
                    var jsonFileName = !string.IsNullOrEmpty(result.OutputJsonPath) ? System.IO.Path.GetFileName(result.OutputJsonPath) : "无";
                    var imageFileName = !string.IsNullOrEmpty(result.OutputImagePath) ? System.IO.Path.GetFileName(result.OutputImagePath) : "无";

                    html += $@"
                    <tr>
                        <td>{result.FileName}</td>
                        <td class='status-success'>{result.DetectionCount}</td>
                        <td>{result.ProcessingTime:HH:mm:ss}</td>
                        <td>{jsonFileName}<br/>{imageFileName}</td>
                    </tr>";
                }

                html += @"
                </tbody>
            </table>
        </div>";
            }

            // 添加失败文件详情
            if (results.Any(r => !r.Success))
            {
                html += @"
        <div class='section'>
            <h2>❌ 处理失败的文件</h2>
            <table>
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>失败原因</th>
                        <th>处理时间</th>
                    </tr>
                </thead>
                <tbody>";

                foreach (var result in results.Where(r => !r.Success))
                {
                    html += $@"
                    <tr>
                        <td>{result.FileName}</td>
                        <td class='status-failed'>{result.ErrorMessage ?? "未知错误"}</td>
                        <td>{result.ProcessingTime:HH:mm:ss}</td>
                    </tr>";
                }

                html += @"
                </tbody>
            </table>
        </div>";
            }

            html += @"
        <div class='footer'>
            <p>📊 报告由医学影像解析系统自动生成</p>
        </div>
    </div>
</body>
</html>";

            return html;
        }

        /// <summary>
        /// 生成CSV格式的详细报告
        /// </summary>
        private string GenerateCsvReport(List<BatchDetectionResult> results)
        {
            var csv = new StringBuilder();

            // CSV 头部
            csv.AppendLine("文件名,文件路径,处理状态,检测数量,处理时间,错误信息,JSON输出文件,图像输出文件");

            // CSV 数据行
            foreach (var result in results)
            {
                var status = result.Success ? "成功" : "失败";
                var errorMessage = result.ErrorMessage?.Replace(",", "，").Replace("\n", " ").Replace("\r", " ") ?? "";
                var jsonFile = !string.IsNullOrEmpty(result.OutputJsonPath) ? System.IO.Path.GetFileName(result.OutputJsonPath) : "";
                var imageFile = !string.IsNullOrEmpty(result.OutputImagePath) ? System.IO.Path.GetFileName(result.OutputImagePath) : "";

                csv.AppendLine($"\"{result.FileName}\",\"{result.FilePath}\",\"{status}\",{result.DetectionCount},\"{result.ProcessingTime:yyyy-MM-dd HH:mm:ss}\",\"{errorMessage}\",\"{jsonFile}\",\"{imageFile}\"");
            }

            return csv.ToString();
        }

        /// <summary>
        /// 打开文件夹预览
        /// </summary>
        private void OpenFolder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var folderDialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "选择包含DICOM文件的文件夹",
                    ShowNewFolderButton = false
                };

                if (folderDialog.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                {
                    return;
                }

                _currentFolderPath = folderDialog.SelectedPath;
                LoadFolderFiles(_currentFolderPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开文件夹失败");
                MessageBox.Show($"打开文件夹失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载文件夹中的文件
        /// </summary>
        private void LoadFolderFiles(string folderPath)
        {
            try
            {
                // 获取DICOM文件
                var dicomFiles = Directory.GetFiles(folderPath, "*.dcm", SearchOption.TopDirectoryOnly)
                    .Concat(Directory.GetFiles(folderPath, "*.dicom", SearchOption.TopDirectoryOnly))
                    .Select(f => new System.IO.FileInfo(f))
                    .OrderBy(f => f.Name)
                    .ToList();

                _currentFolderFiles = dicomFiles;

                // 更新UI
                FolderPathText.Text = $"文件夹: {folderPath} ({dicomFiles.Count} 个文件)";
                FilePreviewListBox.ItemsSource = dicomFiles;
                FilePreviewExpander.Visibility = Visibility.Visible;
                FilePreviewExpander.IsExpanded = true;

                UpdateStatus($"已加载 {dicomFiles.Count} 个DICOM文件");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载文件夹文件失败: {FolderPath}", folderPath);
                MessageBox.Show($"加载文件夹文件失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 文件预览列表选择变化
        /// </summary>
        private async void FilePreviewListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (FilePreviewListBox.SelectedItem is System.IO.FileInfo selectedFile)
            {
                try
                {
                    await LoadDicomImageAsync(selectedFile.FullName);
                    UpdateStatus($"已加载: {selectedFile.Name}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加载选中文件失败: {FileName}", selectedFile.Name);
                    UpdateStatus($"加载文件失败: {selectedFile.Name}");
                }
            }
        }

        /// <summary>
        /// 单个AI检测
        /// </summary>
        private async void SingleAIDetection_Click(object sender, RoutedEventArgs e)
        {
            if (FilePreviewListBox.SelectedItem is not System.IO.FileInfo selectedFile)
            {
                MessageBox.Show("请先选择一个文件。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            if (!_hasImage)
            {
                MessageBox.Show("请先加载图像。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                UpdateStatus("正在进行单个AI检测...");

                // 清除之前的标注
                ClearAnnotations_Click(sender, e);

                // 解析DICOM文件
                var dicomInstance = await _gdcmDicomService.ParseDicomFileAsync(selectedFile.FullName);
                if (dicomInstance == null)
                {
                    MessageBox.Show("无法解析DICOM文件。", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 创建智能标注配置
                var config = new SmartAnnotationConfig
                {
                    EnableMultiModelFusion = true,
                    EnableQualityAssessment = true,
                    EnableSmartFiltering = true,
                    FilterConfig = new SmartFilterConfig
                    {
                        MinConfidenceThreshold = 0.3,
                        MinQualityThreshold = 0.3
                    },
                    ModelConfigs = new List<AutoAnnotationConfig>
                    {
                        new AutoAnnotationConfig
                        {
                            ModelPath = "mock_detection_model",
                            ConfidenceThreshold = 0.3,
                            IouThreshold = 0.45
                        }
                    }
                };

                // 执行AI检测
                var annotationResult = await _smartAnnotationService.GenerateSmartAnnotationsAsync(
                    dicomInstance, config, CancellationToken.None);

                if (annotationResult.Success && annotationResult.FinalAnnotations?.Any() == true)
                {
                    // 显示检测结果
                    DisplayAIAnnotations(annotationResult.FinalAnnotations);

                    var resultMessage = $"单个AI检测完成！\n" +
                                      $"文件: {selectedFile.Name}\n" +
                                      $"检测到 {annotationResult.FinalAnnotations.Count} 个目标";

                    MessageBox.Show(resultMessage, "检测结果",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"文件 {selectedFile.Name} 未检测到任何目标。", "检测结果",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "单个AI检测失败: {FileName}", selectedFile.Name);
                MessageBox.Show($"单个AI检测失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                UpdateStatus("单个AI检测完成");
            }
        }

        /// <summary>
        /// 显示AI标注结果
        /// </summary>
        private void DisplayAIAnnotations(List<Annotation> annotations)
        {
            foreach (var annotation in annotations)
            {
                var aiAnnotation = new AnnotationItem
                {
                    Id = annotation.Id,
                    Type = "矩形",
                    Category = $"AI检测-{annotation.Label}",
                    Confidence = annotation.Confidence,
                    CreatedTime = DateTime.Now
                };

                // 创建可视化形状
                var rectangle = CreateRectangleFromBoundingBox(annotation.BoundingBox);
                rectangle.Stroke = Brushes.Green;
                rectangle.StrokeThickness = 2;
                rectangle.StrokeDashArray = new DoubleCollection { 5, 3 };

                aiAnnotation.Shape = rectangle;
                _annotations.Add(aiAnnotation);
                AnnotationCanvas.Children.Add(rectangle);
            }

            UpdateAnnotationCount();
        }

        /// <summary>
        /// 从边界框创建矩形
        /// </summary>
        private Rectangle CreateRectangleFromBoundingBox(BoundingBox boundingBox)
        {
            var canvasWidth = AnnotationCanvas.ActualWidth > 0 ? AnnotationCanvas.ActualWidth : 800;
            var canvasHeight = AnnotationCanvas.ActualHeight > 0 ? AnnotationCanvas.ActualHeight : 600;

            var left = (boundingBox.CenterX - boundingBox.Width / 2) * canvasWidth;
            var top = (boundingBox.CenterY - boundingBox.Height / 2) * canvasHeight;
            var width = boundingBox.Width * canvasWidth;
            var height = boundingBox.Height * canvasHeight;

            var rectangle = new Rectangle
            {
                Width = width,
                Height = height,
                Fill = Brushes.Transparent
            };

            Canvas.SetLeft(rectangle, left);
            Canvas.SetTop(rectangle, top);

            return rectangle;
        }

        /// <summary>
        /// 文件排序
        /// </summary>
        private void SortFiles_Click(object sender, RoutedEventArgs e)
        {
            if (!_currentFolderFiles.Any())
            {
                MessageBox.Show("没有可排序的文件。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                // 提供排序选项
                var sortOptions = new[]
                {
                    "按名称排序",
                    "按大小排序",
                    "按修改时间排序"
                };

                var selectedOption = Microsoft.VisualBasic.Interaction.InputBox(
                    "请选择排序方式：\n0 - 按名称排序\n1 - 按大小排序\n2 - 按修改时间排序",
                    "文件排序",
                    "0");

                if (int.TryParse(selectedOption, out int option) && option >= 0 && option <= 2)
                {
                    var sortedFiles = option switch
                    {
                        0 => _currentFolderFiles.OrderBy(f => f.Name).ToList(),
                        1 => _currentFolderFiles.OrderBy(f => f.Length).ToList(),
                        2 => _currentFolderFiles.OrderBy(f => f.LastWriteTime).ToList(),
                        _ => _currentFolderFiles
                    };

                    _currentFolderFiles = sortedFiles;
                    FilePreviewListBox.ItemsSource = null;
                    FilePreviewListBox.ItemsSource = _currentFolderFiles;

                    UpdateStatus($"文件已按{sortOptions[option]}重新排序");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "文件排序失败");
                MessageBox.Show($"文件排序失败：{ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    /// <summary>
    /// 标注项目模型
    /// </summary>
    public class AnnotationItem
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = "";
        public string Category { get; set; } = "";
        public double Confidence { get; set; }
        public Shape? Shape { get; set; }
        public DateTime CreatedTime { get; set; }
    }

    /// <summary>
    /// 类别信息
    /// </summary>
    public class CategoryInfo : INotifyPropertyChanged
    {
        private string _name = "";
        private bool _isSystemCategory;
        private string _typeLabel = "";
        private Color _colorIndicator;
        private Color _borderColor;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
            }
        }

        public bool IsSystemCategory
        {
            get => _isSystemCategory;
            set
            {
                _isSystemCategory = value;
                OnPropertyChanged();
            }
        }

        public string TypeLabel
        {
            get => _typeLabel;
            set
            {
                _typeLabel = value;
                OnPropertyChanged();
            }
        }

        public Color ColorIndicator
        {
            get => _colorIndicator;
            set
            {
                _colorIndicator = value;
                OnPropertyChanged();
            }
        }

        public Color BorderColor
        {
            get => _borderColor;
            set
            {
                _borderColor = value;
                OnPropertyChanged();
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 批量标注文件处理结果
    /// </summary>
    public class BatchAnnotationFileResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public int AnnotationCount { get; set; }
        public string? OutputJsonPath { get; set; }
        public string? OutputImagePath { get; set; }
    }

    /// <summary>
    /// 批量标注结果数据
    /// </summary>
    public class BatchAnnotationResult
    {
        public string SourceFile { get; set; } = string.Empty;
        public DateTime ProcessedTime { get; set; }
        public double WindowWidth { get; set; }
        public double WindowCenter { get; set; }
        public List<AnnotationData> Annotations { get; set; } = new();
    }

    /// <summary>
    /// 标注数据
    /// </summary>
    public class AnnotationData
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public BoundingBoxData BoundingBox { get; set; } = new();
        public WindowLevelData WindowLevel { get; set; } = new();
    }

    /// <summary>
    /// 边界框数据
    /// </summary>
    public class BoundingBoxData
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }
    }

    /// <summary>
    /// 窗宽窗位数据
    /// </summary>
    public class WindowLevelData
    {
        public double WindowWidth { get; set; }
        public double WindowCenter { get; set; }
    }



    /// <summary>
    /// 批量检测结果
    /// </summary>
    public class BatchDetectionResult
    {
        public string FilePath { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public int DetectionCount { get; set; }
        public DateTime ProcessingTime { get; set; }
        public string? OutputJsonPath { get; set; }
        public string? OutputImagePath { get; set; }
    }
}
