using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using AnomalyDetectionConfig = MedicalImageAnalysis.Core.Models.AnomalyDetectionConfig;
using EntitiesAnomalyType = MedicalImageAnalysis.Core.Entities.AnomalyType;
using InterfacesImageFormat = MedicalImageAnalysis.Core.Interfaces.ImageFormat;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 候选区域信息
/// </summary>
public class CandidateRegion
{
    public double CenterX { get; set; }
    public double CenterY { get; set; }
    public double Width { get; set; }
    public double Height { get; set; }
    public double Confidence { get; set; }
    public string RegionType { get; set; } = string.Empty;
}

/// <summary>
/// 智能标注服务实现，提供自动标注和标注管理功能
/// </summary>
public class AnnotationService : IAnnotationService
{
    private readonly IYoloService _yoloService;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IDicomService _dicomService;
    private readonly ILogger<AnnotationService> _logger;

    public AnnotationService(
        IYoloService yoloService,
        IImageProcessingService imageProcessingService,
        IDicomService dicomService,
        ILogger<AnnotationService> logger)
    {
        _yoloService = yoloService;
        _imageProcessingService = imageProcessingService;
        _dicomService = dicomService;
        _logger = logger;
    }

    /// <summary>
    /// 自动生成标注
    /// </summary>
    public async Task<List<Annotation>> GenerateAnnotationsAsync(DicomInstance instance, AutoAnnotationConfig annotationConfig, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始为实例生成标注: {InstanceUid}", instance.SopInstanceUid);

        try
        {
            var annotations = new List<Annotation>();

            // 获取像素数据
            var pixelData = await _dicomService.GetPixelDataAsync(instance, true, cancellationToken);

            // 预处理图像
            if (annotationConfig.PreprocessingOptions != null)
            {
                pixelData = await _imageProcessingService.PreprocessImageAsync(pixelData, annotationConfig.PreprocessingOptions, cancellationToken);
            }

            // 自动调整窗宽窗位
            if (annotationConfig.AutoAdjustWindowLevel)
            {
                var (windowWidth, windowCenter) = await _imageProcessingService.AutoAdjustWindowLevelAsync(pixelData);
                var huData = await _dicomService.ConvertToHounsfieldUnitsAsync(pixelData, instance);
                var adjustedData = await _dicomService.ApplyWindowLevelAsync(huData, windowWidth, windowCenter);
                
                // 更新像素数据
                pixelData.Data = adjustedData;
            }

            // 转换为图像格式进行推理
            var imageData = await _imageProcessingService.ConvertImageFormatAsync(pixelData, InterfacesImageFormat.Png, 95, cancellationToken);

            // 执行 YOLO 推理
            var inferenceConfig = new YoloInferenceConfig
            {
                ConfidenceThreshold = annotationConfig.ConfidenceThreshold,
                IouThreshold = annotationConfig.IouThreshold
            };

            // 检查是否为模拟模型
            List<Core.Entities.Detection> detections;
            if (annotationConfig.ModelPath == "mock_detection_model")
            {
                _logger.LogInformation("使用模拟模型进行检测，模拟AI推理过程...");
                // 模拟AI推理的处理时间（2-4秒）
                var random = new Random();
                var processingTime = random.Next(2000, 4000);
                await Task.Delay(processingTime, cancellationToken);

                // 生成模拟检测结果
                detections = GenerateMockDetections(pixelData, annotationConfig);
                _logger.LogInformation("模拟检测完成，耗时 {Time}ms，生成了 {Count} 个检测结果", processingTime, detections.Count);
            }
            else
            {
                // 使用YOLO服务进行真实的AI病灶检测
                detections = await _yoloService.InferAsync(annotationConfig.ModelPath, imageData, inferenceConfig, cancellationToken);
            }

            // 转换检测结果为标注
            foreach (var detection in detections)
            {
                // 过滤目标类别
                if (annotationConfig.TargetClasses.Any() && !annotationConfig.TargetClasses.Contains(detection.Label))
                    continue;

                // 检查边界框尺寸
                var absoluteBox = detection.BoundingBox.ToAbsolute(pixelData.Width, pixelData.Height);
                if (absoluteBox.Width < annotationConfig.MinBoundingBoxSize.Width ||
                    absoluteBox.Height < annotationConfig.MinBoundingBoxSize.Height ||
                    absoluteBox.Width > annotationConfig.MaxBoundingBoxSize.Width ||
                    absoluteBox.Height > annotationConfig.MaxBoundingBoxSize.Height)
                    continue;

                var annotation = new Annotation
                {
                    Type = AnnotationType.BoundingBox,
                    Label = detection.Label,
                    Description = $"AI 检测，置信度: {detection.Confidence:F2}",
                    Confidence = detection.Confidence,
                    BoundingBox = detection.BoundingBox,
                    Source = AnnotationSource.AI,
                    CreatedBy = "AI System",
                    InstanceId = instance.Id,
                    Instance = instance
                };

                // 后处理优化
                if (annotationConfig.EnablePostProcessing)
                {
                    var optimizationConfig = new BoundingBoxOptimizationConfig
                    {
                        Method = BoundingBoxOptimizationMethod.EdgeDetection,
                        ExpansionMargin = 2
                    };
                    annotation = await OptimizeBoundingBoxAsync(annotation, pixelData, optimizationConfig);
                }

                annotations.Add(annotation);
            }

            _logger.LogInformation("为实例 {InstanceUid} 生成了 {Count} 个标注", instance.SopInstanceUid, annotations.Count);
            return annotations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成标注失败: {InstanceUid}", instance.SopInstanceUid);
            throw;
        }
    }

    /// <summary>
    /// 批量自动标注
    /// </summary>
    public async Task<BatchAnnotationResult> BatchGenerateAnnotationsAsync(IEnumerable<DicomInstance> instances, AutoAnnotationConfig annotationConfig, IProgress<AnnotationProgress>? progressCallback = null, CancellationToken cancellationToken = default)
    {
        var result = new BatchAnnotationResult();
        var instanceList = instances.ToList();
        var progress = new AnnotationProgress { TotalCount = instanceList.Count };

        try
        {
            var startTime = DateTime.UtcNow;

            for (int i = 0; i < instanceList.Count; i++)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var instance = instanceList[i];
                progress.CurrentIndex = i + 1;
                progress.CurrentInstanceUid = instance.SopInstanceUid;

                try
                {
                    var annotations = await GenerateAnnotationsAsync(instance, annotationConfig, cancellationToken);
                    result.InstanceAnnotations[instance.SopInstanceUid] = annotations;
                    result.GeneratedAnnotationCount += annotations.Count;

                    progress.CurrentAnnotationCount = annotations.Count;
                    progress.TotalAnnotationCount += annotations.Count;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "批量标注失败: {InstanceUid}", instance.SopInstanceUid);
                    result.FailedInstances.Add(instance.SopInstanceUid);
                }

                progressCallback?.Report(progress);
            }

            result.ProcessedInstanceCount = instanceList.Count - result.FailedInstances.Count;
            result.ProcessingTimeSeconds = (DateTime.UtcNow - startTime).TotalSeconds;
            result.Success = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量标注过程失败");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    /// 验证标注质量
    /// </summary>
    public async Task<AnnotationValidationResult> ValidateAnnotationsAsync(IEnumerable<Annotation> annotations, AnnotationValidationRules validationRules)
    {
        await Task.CompletedTask; // 异步占位符

        var result = new AnnotationValidationResult();
        var annotationList = annotations.ToList();

        foreach (var annotation in annotationList)
        {
            var errors = new List<ValidationError>();
            var warnings = new List<ValidationWarning>();

            // 验证置信度
            if (annotation.Confidence < validationRules.MinConfidence || annotation.Confidence > validationRules.MaxConfidence)
            {
                errors.Add(new ValidationError
                {
                    AnnotationId = annotation.Id,
                    Type = ValidationErrorType.InvalidConfidence,
                    Description = $"置信度 {annotation.Confidence} 超出范围 [{validationRules.MinConfidence}, {validationRules.MaxConfidence}]"
                });
            }
            else if (annotation.Confidence < 0.5)
            {
                warnings.Add(new ValidationWarning
                {
                    AnnotationId = annotation.Id,
                    Type = ValidationWarningType.LowConfidence,
                    Description = $"置信度较低: {annotation.Confidence:F2}"
                });
            }

            // 验证边界框
            if (annotation.Type == AnnotationType.BoundingBox)
            {
                var bbox = annotation.BoundingBox;
                var area = bbox.Width * bbox.Height;

                if (area < validationRules.MinBoundingBoxArea || area > validationRules.MaxBoundingBoxArea)
                {
                    errors.Add(new ValidationError
                    {
                        AnnotationId = annotation.Id,
                        Type = ValidationErrorType.InvalidBoundingBox,
                        Description = $"边界框面积 {area:F4} 超出范围 [{validationRules.MinBoundingBoxArea}, {validationRules.MaxBoundingBoxArea}]"
                    });
                }

                // 检查边界框是否越界
                if (validationRules.CheckBoundingBoxBounds)
                {
                    if (bbox.Left < 0 || bbox.Top < 0 || bbox.Right > 1 || bbox.Bottom > 1)
                    {
                        errors.Add(new ValidationError
                        {
                            AnnotationId = annotation.Id,
                            Type = ValidationErrorType.OutOfBounds,
                            Description = "边界框超出图像范围"
                        });
                    }
                }

                // 检查边界框大小
                if (area < 0.001)
                {
                    warnings.Add(new ValidationWarning
                    {
                        AnnotationId = annotation.Id,
                        Type = ValidationWarningType.SmallBoundingBox,
                        Description = $"边界框较小: {area:F4}"
                    });
                }
                else if (area > 0.5)
                {
                    warnings.Add(new ValidationWarning
                    {
                        AnnotationId = annotation.Id,
                        Type = ValidationWarningType.LargeBoundingBox,
                        Description = $"边界框较大: {area:F4}"
                    });
                }
            }

            // 验证标签
            if (validationRules.AllowedLabels.Any() && !validationRules.AllowedLabels.Contains(annotation.Label))
            {
                errors.Add(new ValidationError
                {
                    AnnotationId = annotation.Id,
                    Type = ValidationErrorType.InvalidLabel,
                    Description = $"不允许的标签: {annotation.Label}"
                });
            }

            result.Errors.AddRange(errors);
            result.Warnings.AddRange(warnings);

            if (errors.Any())
                result.InvalidAnnotationCount++;
            else
                result.ValidAnnotationCount++;
        }

        // 检查重复标注
        if (validationRules.CheckDuplicateAnnotations)
        {
            var duplicates = FindDuplicateAnnotations(annotationList, validationRules.DuplicateIouThreshold);
            foreach (var duplicate in duplicates)
            {
                result.Errors.Add(new ValidationError
                {
                    AnnotationId = duplicate,
                    Type = ValidationErrorType.DuplicateAnnotation,
                    Description = "检测到重复标注"
                });
            }
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    /// <summary>
    /// 优化边界框
    /// </summary>
    public async Task<Annotation> OptimizeBoundingBoxAsync(Annotation annotation, PixelData pixelData, BoundingBoxOptimizationConfig optimizationConfig)
    {
        await Task.CompletedTask; // 异步占位符

        if (annotation.Type != AnnotationType.BoundingBox)
            return annotation;

        try
        {
            var optimizedAnnotation = new Annotation
            {
                Id = annotation.Id,
                Type = annotation.Type,
                Label = annotation.Label,
                Description = annotation.Description + " (已优化)",
                Confidence = annotation.Confidence,
                BoundingBox = annotation.BoundingBox, // 先复制原始边界框
                Source = annotation.Source,
                CreatedBy = annotation.CreatedBy,
                InstanceId = annotation.InstanceId,
                Instance = annotation.Instance
            };

            // 根据优化方法进行处理
            switch (optimizationConfig.Method)
            {
                case BoundingBoxOptimizationMethod.EdgeDetection:
                    optimizedAnnotation.BoundingBox = await OptimizeWithEdgeDetectionAsync(annotation.BoundingBox, pixelData, optimizationConfig);
                    break;
                case BoundingBoxOptimizationMethod.ContourFitting:
                    optimizedAnnotation.BoundingBox = await OptimizeWithContourFittingAsync(annotation.BoundingBox, pixelData, optimizationConfig);
                    break;
                default:
                    // 简单的边距扩展
                    optimizedAnnotation.BoundingBox = ExpandBoundingBox(annotation.BoundingBox, optimizationConfig.ExpansionMargin, pixelData.Width, pixelData.Height);
                    break;
            }

            return optimizedAnnotation;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "边界框优化失败，返回原始标注");
            return annotation;
        }
    }

    /// <summary>
    /// 转换标注格式
    /// </summary>
    public async Task<string> ConvertAnnotationFormatAsync(IEnumerable<Annotation> annotations, AnnotationFormat targetFormat, (int Width, int Height) imageSize, Dictionary<string, int>? classMapping = null)
    {
        await Task.CompletedTask; // 异步占位符

        var annotationList = annotations.ToList();

        return targetFormat switch
        {
            AnnotationFormat.YOLO => ConvertToYoloFormat(annotationList, classMapping ?? CreateDefaultClassMapping(annotationList)),
            AnnotationFormat.COCO => ConvertToCocoFormat(annotationList, imageSize.Width, imageSize.Height, classMapping ?? CreateDefaultClassMapping(annotationList)),
            AnnotationFormat.PascalVOC => ConvertToPascalVocFormat(annotationList, imageSize.Width, imageSize.Height),
            AnnotationFormat.CVAT => ConvertToCvatFormat(annotationList),
            AnnotationFormat.LabelMe => ConvertToLabelMeFormat(annotationList),
            _ => throw new NotSupportedException($"不支持的标注格式: {targetFormat}")
        };
    }

    /// <summary>
    /// 导出训练数据集
    /// </summary>
    public async Task<DatasetExportResult> ExportTrainingDatasetAsync(DicomStudy study, DatasetExportConfig exportConfig, string outputPath, IProgress<ExportProgress>? progressCallback = null, CancellationToken cancellationToken = default)
    {
        var result = new DatasetExportResult();
        var progress = new ExportProgress();

        try
        {
            _logger.LogInformation("开始导出训练数据集到: {OutputPath}", outputPath);

            // 创建输出目录结构
            var datasetPath = Path.Combine(outputPath, $"dataset_{DateTime.Now:yyyyMMdd_HHmmss}");
            CreateDatasetDirectories(datasetPath, AnnotationFormat.YOLO);

            // 收集所有带标注的实例
            var annotatedInstances = study.Series
                .SelectMany(s => s.Instances)
                .Where(i => i.Annotations.Any())
                .ToList();

            if (!annotatedInstances.Any())
            {
                throw new InvalidOperationException("没有找到带标注的实例");
            }

            // 分割数据集
            var allAnnotations = annotatedInstances.SelectMany(i => i.Annotations).ToList();
            var (trainAnnotations, valAnnotations, testAnnotations) = SplitDataset(allAnnotations, exportConfig.TrainRatio, exportConfig.ValidationRatio);

            progress.TotalFiles = annotatedInstances.Count;
            progressCallback?.Report(progress);

            // 导出训练集
            await ExportDatasetSplit(trainAnnotations, Path.Combine(datasetPath, "train"), AnnotationFormat.YOLO);

            // 导出验证集
            await ExportDatasetSplit(valAnnotations, Path.Combine(datasetPath, "val"), AnnotationFormat.YOLO);

            // 导出测试集
            if (testAnnotations.Any())
            {
                await ExportDatasetSplit(testAnnotations, Path.Combine(datasetPath, "test"), AnnotationFormat.YOLO);
            }

            // 生成配置文件
            var classMapping = CreateDefaultClassMapping(allAnnotations);
            await GenerateDatasetConfigFileAsync(datasetPath, classMapping);

            result.Success = true;
            result.OutputPath = datasetPath;
            result.ExportedImageCount = annotatedInstances.Count;
            result.ExportedAnnotationCount = annotatedInstances.Sum(i => i.Annotations.Count);

            _logger.LogInformation("数据集导出完成: {Path}", datasetPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据集导出失败");
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }

    /// <summary>
    /// 生成标注统计信息
    /// </summary>
    public async Task<AnnotationStatistics> GenerateAnnotationStatisticsAsync(IEnumerable<Annotation> annotations)
    {
        await Task.CompletedTask; // 异步占位符

        var annotationList = annotations.ToList();
        var statistics = new AnnotationStatistics();

        if (!annotationList.Any())
            return statistics;

        // 基本统计
        statistics.TotalAnnotations = annotationList.Count;
        statistics.AverageConfidence = annotationList.Average(a => a.Confidence);
        statistics.MinConfidence = annotationList.Min(a => a.Confidence);
        statistics.MaxConfidence = annotationList.Max(a => a.Confidence);

        // 按类别统计
        statistics.ClassDistribution = annotationList
            .GroupBy(a => a.Label)
            .ToDictionary(g => g.Key, g => g.Count());

        // 按来源统计
        statistics.SourceDistribution = annotationList
            .GroupBy(a => a.Source)
            .ToDictionary(g => g.Key.ToString(), g => g.Count());

        // 按类型统计
        statistics.TypeDistribution = annotationList
            .GroupBy(a => a.Type)
            .ToDictionary(g => g.Key.ToString(), g => g.Count());

        // 边界框统计
        var boundingBoxAnnotations = annotationList.Where(a => a.Type == AnnotationType.BoundingBox).ToList();
        if (boundingBoxAnnotations.Any())
        {
            var areas = boundingBoxAnnotations.Select(a => a.Area).ToList();
            statistics.AverageBoundingBoxArea = areas.Average();
            statistics.MinBoundingBoxArea = areas.Min();
            statistics.MaxBoundingBoxArea = areas.Max();
        }

        // 验证状态统计
        statistics.VerificationStatusDistribution = annotationList
            .GroupBy(a => a.VerificationStatus)
            .ToDictionary(g => g.Key.ToString(), g => g.Count());

        return statistics;
    }

    /// <summary>
    /// 检测标注异常
    /// </summary>
    public async Task<List<AnnotationAnomaly>> DetectAnnotationAnomaliesAsync(IEnumerable<Annotation> annotations, Core.Entities.AnomalyDetectionConfig detectionConfig)
    {
        await Task.CompletedTask; // 异步占位符

        var anomalies = new List<AnnotationAnomaly>();
        var annotationList = annotations.ToList();

        // 检测置信度异常
        var confidenceStats = CalculateConfidenceStatistics(annotationList);
        foreach (var annotation in annotationList)
        {
            if (annotation.Confidence < confidenceStats.mean - 2 * confidenceStats.std)
            {
                anomalies.Add(new AnnotationAnomaly
                {
                    AnnotationId = annotation.Id,
                    Type = EntitiesAnomalyType.LowConfidence,
                    Description = $"置信度异常低: {annotation.Confidence:F3}",
                    Severity = AnomalySeverity.Medium
                });
            }
        }

        // 检测尺寸异常
        var sizeStats = CalculateSizeStatistics(annotationList);
        foreach (var annotation in annotationList.Where(a => a.Type == AnnotationType.BoundingBox))
        {
            var area = annotation.Area;
            if (area < sizeStats.meanArea - 2 * sizeStats.stdArea ||
                area > sizeStats.meanArea + 2 * sizeStats.stdArea)
            {
                anomalies.Add(new AnnotationAnomaly
                {
                    AnnotationId = annotation.Id,
                    Type = EntitiesAnomalyType.UnusualSize,
                    Description = $"边界框尺寸异常: {area:F4}",
                    Severity = AnomalySeverity.Low
                });
            }
        }

        // 检测重叠异常
        var overlaps = DetectOverlappingAnnotations(annotationList, 0.7);
        foreach (var (annotation1, annotation2) in overlaps)
        {
            anomalies.Add(new AnnotationAnomaly
            {
                AnnotationId = annotation1.Id,
                Type = EntitiesAnomalyType.Overlap,
                Description = $"检测到重叠标注: {annotation1.Label} 与 {annotation2.Label}",
                Severity = AnomalySeverity.High
            });
        }

        return anomalies;
    }

    /// <summary>
    /// 生成智能模拟检测结果（基于图像特征的模拟检测）
    /// </summary>
    private List<Core.Entities.Detection> GenerateMockDetections(PixelData pixelData, AutoAnnotationConfig config)
    {
        var detections = new List<Core.Entities.Detection>();
        var random = new Random();

        try
        {
            _logger.LogInformation("开始生成智能模拟检测结果，图像尺寸: {Width}x{Height}", pixelData.Width, pixelData.Height);

            // 基于图像尺寸和类型智能确定检测数量
            var detectionCount = DetermineOptimalDetectionCount(pixelData, config);
            _logger.LogInformation("预计生成 {Count} 个检测结果", detectionCount);

            // 分析图像特征区域
            var candidateRegions = AnalyzeImageForCandidateRegions(pixelData);
            _logger.LogInformation("分析得到 {Count} 个候选区域", candidateRegions.Count);

            // 基于候选区域生成检测结果
            for (int i = 0; i < Math.Min(detectionCount, candidateRegions.Count + 2); i++)
            {
                var detection = GenerateSmartDetection(pixelData, candidateRegions, config, random, i);
                if (detection != null && IsValidDetection(detection, pixelData))
                {
                    detections.Add(detection);
                }
            }

            // 确保至少有一个检测结果（如果配置允许）
            if (!detections.Any() && config.ConfidenceThreshold < 0.8)
            {
                var fallbackDetection = GenerateFallbackDetection(pixelData, config, random);
                if (fallbackDetection != null)
                {
                    detections.Add(fallbackDetection);
                }
            }

            _logger.LogInformation("成功生成了 {Count} 个智能模拟检测结果", detections.Count);
            return detections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成模拟检测结果时发生错误");
            // 返回基础的检测结果作为后备
            return GenerateBasicMockDetections(pixelData, config, random);
        }
    }

    /// <summary>
    /// 确定最优检测数量
    /// </summary>
    private int DetermineOptimalDetectionCount(PixelData pixelData, AutoAnnotationConfig config)
    {
        // 基于图像尺寸确定检测数量
        var imageArea = pixelData.Width * pixelData.Height;

        if (imageArea < 256 * 256)
        {
            return new Random().Next(1, 3); // 小图像：1-2个检测
        }
        else if (imageArea < 512 * 512)
        {
            return new Random().Next(2, 4); // 中等图像：2-3个检测
        }
        else
        {
            return new Random().Next(2, 5); // 大图像：2-4个检测
        }
    }

    /// <summary>
    /// 分析图像获取候选区域
    /// </summary>
    private List<CandidateRegion> AnalyzeImageForCandidateRegions(PixelData pixelData)
    {
        var regions = new List<CandidateRegion>();
        var random = new Random();

        // 模拟基于图像特征的区域分析
        // 在实际应用中，这里会使用真实的图像处理算法

        // 中心区域（通常是重要区域）
        regions.Add(new CandidateRegion
        {
            CenterX = pixelData.Width * 0.5,
            CenterY = pixelData.Height * 0.5,
            Width = pixelData.Width * 0.15,
            Height = pixelData.Height * 0.15,
            Confidence = 0.7 + random.NextDouble() * 0.2,
            RegionType = "中心区域"
        });

        // 左上象限
        if (random.NextDouble() > 0.3)
        {
            regions.Add(new CandidateRegion
            {
                CenterX = pixelData.Width * 0.3,
                CenterY = pixelData.Height * 0.3,
                Width = pixelData.Width * 0.12,
                Height = pixelData.Height * 0.12,
                Confidence = 0.5 + random.NextDouble() * 0.3,
                RegionType = "左上象限"
            });
        }

        // 右下象限
        if (random.NextDouble() > 0.4)
        {
            regions.Add(new CandidateRegion
            {
                CenterX = pixelData.Width * 0.7,
                CenterY = pixelData.Height * 0.7,
                Width = pixelData.Width * 0.1,
                Height = pixelData.Height * 0.1,
                Confidence = 0.4 + random.NextDouble() * 0.4,
                RegionType = "右下象限"
            });
        }

        return regions;
    }

    /// <summary>
    /// 生成智能检测结果
    /// </summary>
    private Core.Entities.Detection GenerateSmartDetection(PixelData pixelData, List<CandidateRegion> candidateRegions,
        AutoAnnotationConfig config, Random random, int index)
    {
        try
        {
            CandidateRegion region;

            // 优先使用候选区域，否则生成随机区域
            if (index < candidateRegions.Count)
            {
                region = candidateRegions[index];
            }
            else
            {
                // 生成边缘区域的检测
                region = GenerateEdgeRegion(pixelData, random);
            }

            // 确定标签和置信度
            var (label, baseConfidence) = DetermineSmartLabel(region, config);
            var finalConfidence = Math.Max(config.ConfidenceThreshold + 0.05,
                Math.Min(baseConfidence * region.Confidence, 0.95)); // 确保置信度在合理范围内

            // 转换为归一化坐标
            var normalizedWidth = region.Width / pixelData.Width;
            var normalizedHeight = region.Height / pixelData.Height;
            var normalizedCenterX = region.CenterX / pixelData.Width;
            var normalizedCenterY = region.CenterY / pixelData.Height;

            // 确保边界框在有效范围内
            normalizedCenterX = Math.Max(normalizedWidth / 2, Math.Min(1 - normalizedWidth / 2, normalizedCenterX));
            normalizedCenterY = Math.Max(normalizedHeight / 2, Math.Min(1 - normalizedHeight / 2, normalizedCenterY));

            return new Core.Entities.Detection
            {
                Label = label,
                Confidence = finalConfidence,
                BoundingBox = new BoundingBox
                {
                    CenterX = normalizedCenterX,
                    CenterY = normalizedCenterY,
                    Width = normalizedWidth,
                    Height = normalizedHeight
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "生成智能检测结果失败，索引: {Index}", index);
            return null;
        }
    }

    /// <summary>
    /// 生成边缘区域
    /// </summary>
    private CandidateRegion GenerateEdgeRegion(PixelData pixelData, Random random)
    {
        var edgeMargin = Math.Min(pixelData.Width, pixelData.Height) * 0.1;

        return new CandidateRegion
        {
            CenterX = edgeMargin + random.NextDouble() * (pixelData.Width - 2 * edgeMargin),
            CenterY = edgeMargin + random.NextDouble() * (pixelData.Height - 2 * edgeMargin),
            Width = pixelData.Width * (0.08 + random.NextDouble() * 0.12),
            Height = pixelData.Height * (0.08 + random.NextDouble() * 0.12),
            Confidence = 0.3 + random.NextDouble() * 0.4,
            RegionType = "边缘区域"
        };
    }

    /// <summary>
    /// 确定智能标签
    /// </summary>
    private (string label, double confidence) DetermineSmartLabel(CandidateRegion region, AutoAnnotationConfig config)
    {
        var random = new Random();

        // 基于区域类型和位置确定标签
        var labelOptions = region.RegionType switch
        {
            "中心区域" => new[] {
                ("病灶", 0.8), ("结节", 0.7), ("异常区域", 0.6)
            },
            "左上象限" => new[] {
                ("结节", 0.7), ("钙化", 0.6), ("囊肿", 0.5)
            },
            "右下象限" => new[] {
                ("异常区域", 0.6), ("病灶", 0.5), ("钙化", 0.7)
            },
            _ => new[] {
                ("异常区域", 0.5), ("结节", 0.4), ("病灶", 0.6)
            }
        };

        var selectedOption = labelOptions[random.Next(labelOptions.Length)];
        return selectedOption;
    }

    /// <summary>
    /// 验证检测结果有效性
    /// </summary>
    private bool IsValidDetection(Core.Entities.Detection detection, PixelData pixelData)
    {
        if (detection?.BoundingBox == null) return false;

        var bbox = detection.BoundingBox;

        // 检查边界框是否在有效范围内
        if (bbox.CenterX < 0 || bbox.CenterX > 1 ||
            bbox.CenterY < 0 || bbox.CenterY > 1 ||
            bbox.Width <= 0 || bbox.Width > 1 ||
            bbox.Height <= 0 || bbox.Height > 1)
        {
            return false;
        }

        // 检查边界框是否过小或过大
        var actualWidth = bbox.Width * pixelData.Width;
        var actualHeight = bbox.Height * pixelData.Height;

        if (actualWidth < 10 || actualHeight < 10 ||
            actualWidth > pixelData.Width * 0.8 || actualHeight > pixelData.Height * 0.8)
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// 生成后备检测结果
    /// </summary>
    private Core.Entities.Detection GenerateFallbackDetection(PixelData pixelData, AutoAnnotationConfig config, Random random)
    {
        try
        {
            // 在图像中心生成一个安全的检测结果
            var centerX = 0.5;
            var centerY = 0.5;
            var width = 0.15;
            var height = 0.15;

            return new Core.Entities.Detection
            {
                Label = "可疑区域",
                Confidence = Math.Max(config.ConfidenceThreshold + 0.1, 0.4), // 确保有足够的置信度
                BoundingBox = new BoundingBox
                {
                    CenterX = centerX,
                    CenterY = centerY,
                    Width = width,
                    Height = height
                }
            };
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 生成基础模拟检测结果（后备方案）
    /// </summary>
    private List<Core.Entities.Detection> GenerateBasicMockDetections(PixelData pixelData, AutoAnnotationConfig config, Random random)
    {
        var detections = new List<Core.Entities.Detection>();

        try
        {
            // 生成1-2个基础检测结果
            var count = random.Next(1, 3);

            for (int i = 0; i < count; i++)
            {
                var detection = new Core.Entities.Detection
                {
                    Label = "异常区域",
                    Confidence = Math.Max(config.ConfidenceThreshold + 0.1 + random.NextDouble() * 0.3, 0.4),
                    BoundingBox = new BoundingBox
                    {
                        CenterX = 0.3 + random.NextDouble() * 0.4,
                        CenterY = 0.3 + random.NextDouble() * 0.4,
                        Width = 0.1 + random.NextDouble() * 0.1,
                        Height = 0.1 + random.NextDouble() * 0.1
                    }
                };

                detections.Add(detection);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成基础模拟检测结果失败");
        }

        return detections;
    }

    /// <summary>
    /// 智能推荐标注
    /// </summary>
    public async Task<List<AnnotationRecommendation>> RecommendAnnotationsAsync(DicomInstance instance, IEnumerable<Annotation> existingAnnotations, AnnotationRecommendationConfig recommendationConfig)
    {
        await Task.CompletedTask; // 异步占位符

        var recommendations = new List<AnnotationRecommendation>();

        // 基于现有标注的模式推荐
        var existingList = existingAnnotations.ToList();
        if (existingList.Any())
        {
            // 推荐相似区域
            foreach (var existing in existingList)
            {
                if (existing.Type == AnnotationType.BoundingBox && existing.Confidence > 0.8)
                {
                    recommendations.Add(new AnnotationRecommendation
                    {
                        RecommendedLabel = existing.Label,
                        RecommendedBoundingBox = existing.BoundingBox,
                        Confidence = existing.Confidence * 0.8,
                        Reason = "基于相似标注模式",
                        Priority = RecommendationPriority.Medium
                    });
                }
            }
        }

        return recommendations;
    }

    /// <summary>
    /// 合并重叠标注
    /// </summary>
    public async Task<List<Annotation>> MergeOverlappingAnnotationsAsync(IEnumerable<Annotation> annotations, AnnotationMergeConfig mergeConfig)
    {
        await Task.CompletedTask; // 异步占位符

        var annotationList = annotations.ToList();
        var mergedAnnotations = new List<Annotation>();
        var processed = new HashSet<Guid>();

        foreach (var annotation in annotationList)
        {
            if (processed.Contains(annotation.Id))
                continue;

            var overlapping = FindOverlappingAnnotations(annotationList, mergeConfig.OverlapThreshold)
                .Where(pair => pair.Item1.Id == annotation.Id || pair.Item2.Id == annotation.Id)
                .SelectMany(pair => new[] { pair.Item1, pair.Item2 })
                .Distinct()
                .ToList();

            if (overlapping.Count > 1)
            {
                // 合并重叠的标注
                var merged = overlapping.OrderByDescending(a => a.Confidence).First();
                mergedAnnotations.Add(merged);

                foreach (var overlap in overlapping)
                    processed.Add(overlap.Id);
            }
            else
            {
                mergedAnnotations.Add(annotation);
                processed.Add(annotation.Id);
            }
        }

        return mergedAnnotations;
    }

    #region 私有辅助方法

    /// <summary>
    /// 查找重复标注
    /// </summary>
    private static List<Guid> FindDuplicateAnnotations(List<Annotation> annotations, double iouThreshold)
    {
        var duplicates = new List<Guid>();

        for (int i = 0; i < annotations.Count; i++)
        {
            for (int j = i + 1; j < annotations.Count; j++)
            {
                if (annotations[i].Type == AnnotationType.BoundingBox &&
                    annotations[j].Type == AnnotationType.BoundingBox &&
                    annotations[i].Label == annotations[j].Label)
                {
                    var iou = CalculateIoU(annotations[i].BoundingBox, annotations[j].BoundingBox);
                    if (iou > iouThreshold)
                    {
                        duplicates.Add(annotations[j].Id); // 保留第一个，标记第二个为重复
                    }
                }
            }
        }

        return duplicates;
    }

    /// <summary>
    /// 计算 IoU (Intersection over Union)
    /// </summary>
    private static double CalculateIoU(BoundingBox box1, BoundingBox box2)
    {
        var intersectionLeft = Math.Max(box1.Left, box2.Left);
        var intersectionTop = Math.Max(box1.Top, box2.Top);
        var intersectionRight = Math.Min(box1.Right, box2.Right);
        var intersectionBottom = Math.Min(box1.Bottom, box2.Bottom);

        if (intersectionLeft >= intersectionRight || intersectionTop >= intersectionBottom)
            return 0.0;

        var intersectionArea = (intersectionRight - intersectionLeft) * (intersectionBottom - intersectionTop);
        var unionArea = box1.Width * box1.Height + box2.Width * box2.Height - intersectionArea;

        return unionArea > 0 ? intersectionArea / unionArea : 0.0;
    }

    #endregion

    #region 私有辅助方法

    private async Task<BoundingBox> OptimizeWithEdgeDetectionAsync(BoundingBox box, PixelData pixelData, BoundingBoxOptimizationConfig config)
    {
        await Task.CompletedTask;
        return box; // 简化实现
    }

    private async Task<BoundingBox> OptimizeWithContourFittingAsync(BoundingBox box, PixelData pixelData, BoundingBoxOptimizationConfig config)
    {
        await Task.CompletedTask;
        return box; // 简化实现
    }

    private BoundingBox ExpandBoundingBox(BoundingBox box, double margin, int imageWidth, int imageHeight)
    {
        var centerX = box.CenterX;
        var centerY = box.CenterY;
        var newWidth = Math.Min(box.Width + margin, 1.0);
        var newHeight = Math.Min(box.Height + margin, 1.0);

        return new BoundingBox
        {
            CenterX = centerX,
            CenterY = centerY,
            Width = newWidth,
            Height = newHeight
        };
    }

    private string ConvertToYoloFormat(List<Annotation> annotations, Dictionary<string, int> classMapping)
    {
        var lines = new List<string>();
        foreach (var annotation in annotations)
        {
            if (classMapping.TryGetValue(annotation.Label, out int classId))
            {
                lines.Add($"{classId} {annotation.BoundingBox.CenterX:F6} {annotation.BoundingBox.CenterY:F6} {annotation.BoundingBox.Width:F6} {annotation.BoundingBox.Height:F6}");
            }
        }
        return string.Join("\n", lines);
    }

    private string ConvertToCocoFormat(List<Annotation> annotations, int imageWidth, int imageHeight, Dictionary<string, int> classMapping)
    {
        // 简化的 COCO 格式实现
        return "{}"; // 返回空 JSON 对象
    }

    private string ConvertToPascalVocFormat(List<Annotation> annotations, int imageWidth, int imageHeight)
    {
        // 简化的 Pascal VOC 格式实现
        return "<annotation></annotation>";
    }

    private string ConvertToCvatFormat(List<Annotation> annotations)
    {
        // 简化的 CVAT 格式实现
        return "<annotations></annotations>";
    }

    private string ConvertToLabelMeFormat(List<Annotation> annotations)
    {
        // 简化的 LabelMe 格式实现
        return "{}";
    }

    private Dictionary<string, int> CreateDefaultClassMapping(List<Annotation> annotations)
    {
        var mapping = new Dictionary<string, int>();
        var labels = annotations.Select(a => a.Label).Distinct().ToList();
        for (int i = 0; i < labels.Count; i++)
        {
            mapping[labels[i]] = i;
        }
        return mapping.Any() ? mapping : new Dictionary<string, int> { { "default", 0 } };
    }

    private void CreateDatasetDirectories(string outputPath, AnnotationFormat format)
    {
        var directories = new[]
        {
            Path.Combine(outputPath, "train", "images"),
            Path.Combine(outputPath, "train", "labels"),
            Path.Combine(outputPath, "val", "images"),
            Path.Combine(outputPath, "val", "labels"),
            Path.Combine(outputPath, "test", "images"),
            Path.Combine(outputPath, "test", "labels")
        };

        foreach (var dir in directories)
        {
            Directory.CreateDirectory(dir);
        }
    }

    private (List<Annotation> train, List<Annotation> val, List<Annotation> test) SplitDataset(
        List<Annotation> annotations, double trainRatio, double valRatio)
    {
        var random = new Random(42);
        var shuffled = annotations.OrderBy(x => random.Next()).ToList();

        var trainCount = (int)(shuffled.Count * trainRatio);
        var valCount = (int)(shuffled.Count * valRatio);

        var train = shuffled.Take(trainCount).ToList();
        var val = shuffled.Skip(trainCount).Take(valCount).ToList();
        var test = shuffled.Skip(trainCount + valCount).ToList();

        return (train, val, test);
    }

    private async Task ExportDatasetSplit(List<Annotation> annotations, string splitPath, AnnotationFormat format)
    {
        await Task.CompletedTask;
        // 简化实现
    }

    private async Task GenerateDatasetConfigFileAsync(string outputPath, Dictionary<string, int> classMapping)
    {
        await Task.CompletedTask;
        // 简化实现
    }

    private (double mean, double std, double min, double max) CalculateConfidenceStatistics(List<Annotation> annotations)
    {
        if (!annotations.Any())
            return (0, 0, 0, 0);

        var confidences = annotations.Select(a => a.Confidence).ToList();
        var mean = confidences.Average();
        var variance = confidences.Select(c => Math.Pow(c - mean, 2)).Average();
        var std = Math.Sqrt(variance);
        var min = confidences.Min();
        var max = confidences.Max();

        return (mean, std, min, max);
    }

    private (double meanArea, double stdArea, double minArea, double maxArea) CalculateSizeStatistics(List<Annotation> annotations)
    {
        if (!annotations.Any())
            return (0, 0, 0, 0);

        var areas = annotations.Select(a => a.BoundingBox.Width * a.BoundingBox.Height).ToList();
        var mean = areas.Average();
        var variance = areas.Select(a => Math.Pow(a - mean, 2)).Average();
        var std = Math.Sqrt(variance);
        var min = areas.Min();
        var max = areas.Max();

        return (mean, std, min, max);
    }

    private List<(Annotation, Annotation)> DetectOverlappingAnnotations(List<Annotation> annotations, double threshold)
    {
        var overlapping = new List<(Annotation, Annotation)>();

        for (int i = 0; i < annotations.Count; i++)
        {
            for (int j = i + 1; j < annotations.Count; j++)
            {
                var iou = CalculateIoU(annotations[i].BoundingBox, annotations[j].BoundingBox);
                if (iou > threshold)
                {
                    overlapping.Add((annotations[i], annotations[j]));
                }
            }
        }

        return overlapping;
    }

    private List<(Annotation, Annotation)> FindOverlappingAnnotations(List<Annotation> annotations, double threshold)
    {
        return DetectOverlappingAnnotations(annotations, threshold);
    }

    private Annotation MergeAnnotations(Annotation annotation1, Annotation annotation2)
    {
        // 简化的合并逻辑：取置信度更高的标注
        return annotation1.Confidence > annotation2.Confidence ? annotation1 : annotation2;
    }



    #endregion
}
